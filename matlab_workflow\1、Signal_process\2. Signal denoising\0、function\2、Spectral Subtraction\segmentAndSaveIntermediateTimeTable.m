function segmentAndSaveIntermediateTimeTable(tt_ch2, tt_ch3, baseFileName, outputFolder, config, resultType)
%SEGMENTANDSAVEINTERMEDIATETIMETABLE 分割并保存中间结果时间表
%   专门用于处理中间结果（带通滤波、谱减法）的分割和保存。
%   该函数基于主分割函数，但针对中间结果进行了优化和定制。
%
%   语法:
%   segmentAndSaveIntermediateTimeTable(tt_ch2, tt_ch3, baseFileName, outputFolder, config, resultType)
%
%   输入参数:
%   tt_ch2       - CSV第2列的时间表数据（中间处理结果）
%   tt_ch3       - CSV第3列的时间表数据（中间处理结果）
%   baseFileName - 基础文件名（已包含结果类型）
%   outputFolder - 输出文件夹路径
%   config       - 处理配置参数结构体
%   resultType   - 结果类型标识 ('bandpass' 或 'spectral')
%
%   输出结果:
%   根据配置在指定文件夹的子文件夹中生成分割文件：
%   - original_timeline/: 保持原始连续时间刻度的分割文件
%   - reset_timeline/: 每个文件从0开始重新计时的分割文件
%   文件命名格式: [baseFileName]_seg[序号]_tt.mat
%   每个文件包含: [baseFileName]_seg[序号]_tt1, [baseFileName]_seg[序号]_tt2
%
%   配置参数:
%   config.secondarySegmentLength - 二次分割长度（秒）
%   config.secondaryOverlapRatio  - 重叠比例（0-0.5）
%   config.minSegmentLength      - 最小片段长度（秒）
%   config.samplingRate          - 采样率（Hz）
%   config.enableDualTimeScale   - 启用双时间刻度保存
%   config.originalTimelineFolder - 原始时间刻度文件夹名
%   config.resetTimelineFolder   - 重置时间刻度文件夹名
%   config.saveOriginalTimeline  - 保存原始连续时间刻度
%   config.saveResetTimeline     - 保存重置时间刻度
%
%   处理流程:
%   1. 参数验证和分割计算
%   2. 创建双时间刻度文件夹结构
%   3. 按配置进行时间表分割
%   4. 保存分割后的文件到相应文件夹
%   5. 生成处理报告
%
%   示例:
%   % 保存带通滤波分割结果
%   segmentAndSaveIntermediateTimeTable(tt_ch2, tt_ch3, 'data1_bandpass', 
%                                      './bandpass_results', config, 'bandpass');
%
%   注意事项:
%   - 分割参数与主处理流程保持一致
%   - 文件命名包含结果类型标识
%   - 支持与主结果相同的双时间刻度功能
%   - 自动处理文件夹创建和错误处理
%
%   参见: SEGMENTANDSAVETIMETABLE, SAVEINTERMEDIATERESULTS
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    %% 参数验证和初始化
    if nargin < 6
        error('segmentAndSaveIntermediateTimeTable:NotEnoughInputs', '需要6个输入参数');
    end
    
    % 验证时间表数据
    if height(tt_ch2) ~= height(tt_ch3)
        error('segmentAndSaveIntermediateTimeTable:DimensionMismatch', '两个通道的时间表长度不匹配');
    end

    % 获取基本参数
    totalSamples = height(tt_ch2);
    samplingRate = config.samplingRate;
    totalDuration = totalSamples / samplingRate;
    
    % 计算分割参数
    segmentLengthSamples = round(config.secondarySegmentLength * samplingRate);
    overlapSamples = round(segmentLengthSamples * config.secondaryOverlapRatio);
    stepSamples = segmentLengthSamples - overlapSamples;
    minSegmentSamples = round(config.minSegmentLength * samplingRate);
    
    % 计算总的片段数量
    if totalSamples <= segmentLengthSamples
        numSegments = 1;
    else
        numCompleteSegments = floor((totalSamples - segmentLengthSamples) / stepSamples) + 1;
        lastSegmentStart = (numCompleteSegments - 1) * stepSamples + 1;
        remainingSamples = totalSamples - lastSegmentStart + 1;

        if remainingSamples > segmentLengthSamples
            numSegments = numCompleteSegments + 1;
        else
            numSegments = numCompleteSegments;
        end
    end
    
    % 清理基础文件名
    cleanBaseName = regexprep(baseFileName, '[^a-zA-Z0-9_]', '_');
    if ~isempty(cleanBaseName) && ~isletter(cleanBaseName(1))
        cleanBaseName = ['file_', cleanBaseName];
    end

    %% 创建双时间刻度文件夹
    originalTimelineFolder = '';
    resetTimelineFolder = '';

    if config.enableDualTimeScale
        if config.saveOriginalTimeline
            originalTimelineFolder = fullfile(outputFolder, config.originalTimelineFolder);
            if ~exist(originalTimelineFolder, 'dir')
                mkdir(originalTimelineFolder);
                if config.enableVerboseOutput
                    fprintf('创建%s原始时间刻度文件夹: %s\n', resultType, originalTimelineFolder);
                end
            end
        end

        if config.saveResetTimeline
            resetTimelineFolder = fullfile(outputFolder, config.resetTimelineFolder);
            if ~exist(resetTimelineFolder, 'dir')
                mkdir(resetTimelineFolder);
                if config.enableVerboseOutput
                    fprintf('创建%s重置时间刻度文件夹: %s\n', resultType, resetTimelineFolder);
                end
            end
        end
    end
    
    %% 显示分割信息
    if config.enableVerboseOutput
        fprintf('=== %s结果分割信息 ===\n', upper(resultType));
        fprintf('原始时长: %.2f 秒 (%d 样本)\n', totalDuration, totalSamples);
        fprintf('片段长度: %.2f 秒 (%d 样本)\n', config.secondarySegmentLength, segmentLengthSamples);
        fprintf('重叠长度: %.2f 秒 (%d 样本)\n', overlapSamples/samplingRate, overlapSamples);
        fprintf('预计片段数: %d\n', numSegments);
        fprintf('==================\n');
    end
    
    %% 执行分割和保存
    savedCount = 0;
    
    for segIdx = 1:numSegments
        % 计算当前片段的起始和结束位置
        startIdx = (segIdx - 1) * stepSamples + 1;
        endIdx = min(startIdx + segmentLengthSamples - 1, totalSamples);
        currentSegmentLength = endIdx - startIdx + 1;

        if config.enableVerboseOutput
            fprintf('%s片段 %d: 起始=%d, 结束=%d, 长度=%d样本(%.2f秒)\n', ...
                resultType, segIdx, startIdx, endIdx, currentSegmentLength, currentSegmentLength/samplingRate);
        end

        if currentSegmentLength < minSegmentSamples
            if config.enableVerboseOutput
                fprintf('  -> %s片段 %d 长度不足，跳过保存\n', resultType, segIdx);
            end
            continue;
        end
        
        % 提取当前片段的数据
        segmentTT_ch2_original = tt_ch2(startIdx:endIdx, :);
        segmentTT_ch3_original = tt_ch3(startIdx:endIdx, :);

        % 生成片段文件名和变量名
        segmentSuffix = sprintf('_seg%03d', segIdx);
        var1Name = [cleanBaseName, segmentSuffix, '_tt1'];
        var2Name = [cleanBaseName, segmentSuffix, '_tt2'];

        % 保存数据到不同的时间刻度文件夹
        segmentSaved = false;

        try
            % 保存原始时间刻度版本
            if config.enableDualTimeScale && config.saveOriginalTimeline
                originalFileName = fullfile(originalTimelineFolder, [cleanBaseName, segmentSuffix, '_tt.mat']);

                eval([var1Name, ' = segmentTT_ch2_original;']);
                eval([var2Name, ' = segmentTT_ch3_original;']);
                save(originalFileName, var1Name, var2Name);

                if config.enableProgressDisplay
                    fprintf('已保存%s原始时间刻度片段 %d: %s\n', ...
                        resultType, segIdx, [cleanBaseName, segmentSuffix, '_tt.mat']);
                end
                segmentSaved = true;
            end

            % 保存重置时间刻度版本
            if config.enableDualTimeScale && config.saveResetTimeline
                resetFileName = fullfile(resetTimelineFolder, [cleanBaseName, segmentSuffix, '_tt.mat']);

                % 创建重置时间刻度的时间表
                segmentTT_ch2_reset = createResetTimeTable(segmentTT_ch2_original, config.samplingRate);
                segmentTT_ch3_reset = createResetTimeTable(segmentTT_ch3_original, config.samplingRate);

                eval([var1Name, ' = segmentTT_ch2_reset;']);
                eval([var2Name, ' = segmentTT_ch3_reset;']);
                save(resetFileName, var1Name, var2Name);

                if config.enableProgressDisplay
                    fprintf('已保存%s重置时间刻度片段 %d: %s\n', ...
                        resultType, segIdx, [cleanBaseName, segmentSuffix, '_tt.mat']);
                end
                segmentSaved = true;
            end

            % 如果没有启用双时间刻度，使用原有逻辑
            if ~config.enableDualTimeScale
                segmentFileName = fullfile(outputFolder, [cleanBaseName, segmentSuffix, '_tt.mat']);
                eval([var1Name, ' = segmentTT_ch2_original;']);
                eval([var2Name, ' = segmentTT_ch3_original;']);
                save(segmentFileName, var1Name, var2Name);
                segmentSaved = true;

                if config.enableProgressDisplay
                    fprintf('已保存%s片段 %d: %s\n', ...
                        resultType, segIdx, [cleanBaseName, segmentSuffix, '_tt.mat']);
                end
            end

            if segmentSaved
                savedCount = savedCount + 1;
            end

        catch ME
            if config.enableErrorHandling
                warning('segmentAndSaveIntermediateTimeTable:SaveFailed', ...
                    '%s片段 %d 保存失败: %s', resultType, segIdx, ME.message);
            else
                rethrow(ME);
            end
        end
    end
    
    %% 输出总结信息
    if config.enableVerboseOutput
        fprintf('\n=== %s结果分割完成 ===\n', upper(resultType));
        fprintf('成功保存片段数: %d/%d\n', savedCount, numSegments);
        if config.enableDualTimeScale
            if config.saveOriginalTimeline
                fprintf('原始时间刻度文件夹: %s\n', originalTimelineFolder);
            end
            if config.saveResetTimeline
                fprintf('重置时间刻度文件夹: %s\n', resetTimelineFolder);
            end
        else
            fprintf('输出文件夹: %s\n', outputFolder);
        end
        fprintf('===================\n\n');
    end
end

function resetTT = createResetTimeTable(originalTT, samplingRate)
%CREATERESETTIMETABLE 创建重置时间刻度的时间表
    data = originalTT.Variables;
    resetTT = timetable(data, 'SampleRate', samplingRate);
end
