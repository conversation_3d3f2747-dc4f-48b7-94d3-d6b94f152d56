%% GUI模式数据合并脚本启动器
%RUN_MERGE_GUI 启动交互式文件选择的数据合并脚本
%   该脚本启动GUI模式的数据合并功能，允许用户通过文件对话框
%   选择要合并的特定数据文件。
%
%   使用方法:
%   A_run_merge_gui
%
%   功能:
%   - 打开文件选择对话框
%   - 支持多文件选择（Ctrl+点击或Shift+点击）
%   - 显示文件信息供用户确认
%   - 检查段号连续性
%   - 合并选择的文件到单个.mat文件（包含tt1和tt2）
%
%   输出文件名格式:
%   merged_dataX_selected_segments_YYYYMMDD_HHMMSS.mat
%
%   作者: [数据处理团队]
%   创建日期: 2025-08-26

fprintf('启动GUI模式数据合并脚本...\n');
C_merge_data_files(true);

