function plot_PersistenceSpectrum(signal, fs, position, rows, cols, index, title_text, varargin)
%PLOT_PERSISTENCESPECTRUM 肠鸣音信号持久谱绘制函数
%   计算并绘制信号的持久频谱（Persistence Spectrum），专门用于肠鸣音音频信号的
%   概率统计频域分析。该函数提供专业的科研级别持久谱可视化，适用于医学信号
%   处理和肠鸣音频域统计特征分析研究。
%
%   持久谱显示了信号频域功率分布的概率统计特征，能够展示不同频率成分
%   在时间上的持续性和出现概率，特别适用于分析肠鸣音等非平稳生理信号。
%
%   语法:
%   plot_PersistenceSpectrum(signal, fs, position, rows, cols, index, title_text)
%   plot_PersistenceSpectrum(signal, fs, position, rows, cols, index, title_text, time_range)
%   plot_PersistenceSpectrum(signal, fs, position, rows, cols, index, title_text, time_range, freq_range)
%   plot_PersistenceSpectrum(signal, fs, position, rows, cols, index, title_text, time_range, freq_range, overlap_percent)
%
%   输入参数:
%   signal          - 输入信号数据 (数值向量)
%   fs              - 采样频率 (标量，单位：Hz，通常为2570Hz)
%   position        - 图形窗口位置 ([left, bottom, width, height])
%   rows            - 子图行数 (正整数)
%   cols            - 子图列数 (正整数)
%   index           - 当前子图索引 (正整数)
%   title_text      - 图形标题 (字符串)
%   time_range      - 可选，时间轴范围 ([t_min, t_max]，单位：秒)
%   freq_range      - 可选，频率轴范围 ([f_min, f_max]，单位：Hz)
%   overlap_percent - 可选，重叠百分比 (0-99，默认为50)
%
%   输出参数:
%   无 - 直接生成持久谱图显示
%
%   技术参数:
%   - 默认频率范围: [0, 1285] Hz (适合肠鸣音分析)
%   - 默认时间范围: 整个信号长度
%   - 默认重叠率: 50%
%   - 持久谱算法: MATLAB内置pspectrum函数的persistence模式
%   - 颜色映射: inferno (感知均匀颜色映射)
%   - 字体设置: Times New Roman, 加粗, 16pt刻度, 18pt标签
%
%   图形特征:
%   - X轴: 频率 (Hz)
%   - Y轴: 功率谱密度 (dB)
%   - 颜色条: 出现概率百分比 (%)
%   - 网格: 开启，便于读数
%
%   应用场景:
%   - 肠鸣音信号频域统计特征分析
%   - 信号频率成分持续性评估
%   - 非平稳信号的概率频域分析
%   - 信号质量和稳定性评估
%
%   使用示例:
%   % 基本调用
%   plot_PersistenceSpectrum(signal, 2570, [100,100,800,600], 1, 1, 1, 'Persistence Spectrum');
%
%   % 指定时间范围
%   plot_PersistenceSpectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Persistence Spectrum', [0, 60]);
%
%   % 指定时间和频率范围
%   plot_PersistenceSpectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Persistence Spectrum', [0, 60], [0, 1000]);
%
%   % 指定所有参数
%   plot_PersistenceSpectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Persistence Spectrum', [0, 60], [0, 1000], 75);
%
%   注意事项:
%   - 输入信号应为一维数值向量
%   - 采样频率必须为正数
%   - 时间范围和频率范围应为递增的二元素向量
%   - 重叠百分比应在0-99范围内
%   - 持久谱计算需要足够长的信号以获得可靠的统计结果
%
%   错误处理:
%   - 输入参数类型和维度验证
%   - 采样频率有效性检查
%   - 时间和频率范围合理性验证
%   - 信号长度充足性检查
%   - 重叠百分比范围验证
%
%   参见: PSPECTRUM, PLOT_PSPECTRUM, PLOT_SPECTROGRAM, INFERNO
%
%   作者: [医学信号处理团队]
%   创建日期: 2025-08-26
%   最后修改: 2025-08-26
%   版本: 1.0

    % 输入参数验证
    if nargin < 7
        error('plot_PersistenceSpectrum:NotEnoughInputs', '至少需要7个输入参数');
    end

    % 验证信号数据
    if ~isnumeric(signal) || ~isvector(signal)
        error('plot_PersistenceSpectrum:InvalidSignal', '信号数据必须是数值向量');
    end

    % 验证采样频率
    if ~isnumeric(fs) || ~isscalar(fs) || fs <= 0
        error('plot_PersistenceSpectrum:InvalidSamplingRate', '采样频率必须是正数标量');
    end

    % 验证位置参数
    if ~isnumeric(position) || length(position) ~= 4
        error('plot_PersistenceSpectrum:InvalidPosition', '位置参数必须是4元素数值向量');
    end

    % 验证子图参数
    if ~isnumeric(rows) || ~isscalar(rows) || rows <= 0 || rows ~= round(rows)
        error('plot_PersistenceSpectrum:InvalidRows', '行数必须是正整数');
    end
    if ~isnumeric(cols) || ~isscalar(cols) || cols <= 0 || cols ~= round(cols)
        error('plot_PersistenceSpectrum:InvalidCols', '列数必须是正整数');
    end
    if ~isnumeric(index) || ~isscalar(index) || index <= 0 || index ~= round(index)
        error('plot_PersistenceSpectrum:InvalidIndex', '索引必须是正整数');
    end

    % 验证标题
    if ~ischar(title_text) && ~isstring(title_text)
        error('plot_PersistenceSpectrum:InvalidTitle', '标题必须是字符串');
    end
    
    % 处理可选参数
    time_range = [];
    freq_range = [0, 1285];  % 默认频率范围，适合肠鸣音分析
    overlap_percent = 50;    % 默认重叠百分比
    
    if nargin >= 8 && ~isempty(varargin{1})
        time_range = varargin{1};
        if ~isnumeric(time_range) || length(time_range) ~= 2 || time_range(1) >= time_range(2)
            error('plot_PersistenceSpectrum:InvalidTimeRange', '时间范围必须是递增的二元素向量');
        end
    end

    if nargin >= 9 && ~isempty(varargin{2})
        freq_range = varargin{2};
        if ~isnumeric(freq_range) || length(freq_range) ~= 2 || freq_range(1) >= freq_range(2)
            error('plot_PersistenceSpectrum:InvalidFreqRange', '频率范围必须是递增的二元素向量');
        end
    end
    
    if nargin >= 10 && ~isempty(varargin{3})
        overlap_percent = varargin{3};
        if ~isnumeric(overlap_percent) || ~isscalar(overlap_percent) || overlap_percent < 0 || overlap_percent >= 100
            error('plot_PersistenceSpectrum:InvalidOverlap', '重叠百分比必须在0-99范围内');
        end
    end
    
    % 确保信号为列向量
    signal = signal(:);
    
    % 计算信号时间向量
    N = length(signal);
    t = (0:N-1) / fs;
    
    % 处理时间范围
    if ~isempty(time_range)
        % 找到时间范围内的索引
        start_idx = max(1, round(time_range(1) * fs) + 1);
        end_idx = min(N, round(time_range(2) * fs) + 1);
        
        if start_idx >= end_idx
            error('plot_PersistenceSpectrum:InvalidTimeRange', '指定的时间范围超出信号长度');
        end
        
        % 截取信号
        signal = signal(start_idx:end_idx);
        t = t(start_idx:end_idx);
    end
    
    % 验证信号长度足够计算持久谱
    if length(signal) < fs * 0.1  % 至少需要0.1秒的数据
        error('plot_PersistenceSpectrum:SignalTooShort', '信号长度不足以计算可靠的持久谱');
    end
    
    % 创建图形
    figure('Position', position);
    subplot(rows, cols, index);
    
    % 计算并绘制持久谱
    try
        % 使用pspectrum函数的persistence模式
        pspectrum(signal, fs, 'persistence', ...
                  'FrequencyLimits', freq_range, ...
                  'OverlapPercent', overlap_percent);
        
        % 获取当前轴句柄并设置属性
        ax = gca;
        ax.FontSize = 16;
        ax.FontName = 'Times New Roman';
        ax.FontWeight = 'bold';
        
        % 设置坐标轴标签
        xlabel('Frequency (Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
        ylabel('Power Spectral Density (dB)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
        title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
        
        % 设置颜色条标签
        cb = colorbar;
        cb.FontSize = 16;
        cb.FontName = 'Times New Roman';
        cb.FontWeight = 'bold';
        cb.Label.String = 'Percentage of Time (%)';
        cb.Label.FontSize = 18;
        cb.Label.FontName = 'Times New Roman';
        cb.Label.FontWeight = 'bold';
        
        % 应用inferno颜色映射（如果可用）
        try
            colormap("default");
        catch
            % 如果inferno不可用，使用默认的热图颜色映射inferno
            colormap(hot);
        end
        
        % 开启网格
        grid on;
        ax.GridAlpha = 0.3;
        
    catch ME
        error('plot_PersistenceSpectrum:PspectrumError', '持久谱计算失败: %s', ME.message);
    end
    
end
