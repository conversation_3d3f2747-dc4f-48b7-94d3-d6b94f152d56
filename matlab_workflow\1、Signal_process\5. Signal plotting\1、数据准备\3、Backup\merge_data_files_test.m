%% 数据文件合并脚本 - 测试版本
%MERGE_DATA_FILES_TEST 测试版本的数据合并脚本
%   该版本用于测试交互式文件选择功能，在批处理模式下运行
%   自动选择部分文件进行测试

%% 初始化
clear all;
close all;
clc;

% 设置路径
raw_data_folder = '1、Raw data';
processed_data_folder = '2、Processed data';

% 确保输出文件夹存在
if ~exist(processed_data_folder, 'dir')
    mkdir(processed_data_folder);
end

fprintf('=== 数据文件合并脚本 - 测试版本 ===\n');

%% 模拟文件选择（测试用）
fprintf('模拟文件选择...\n');

% 获取所有可用文件
available_files = dir(fullfile(raw_data_folder, '*.mat'));
if isempty(available_files)
    error('在%s文件夹中未找到任何.mat文件', raw_data_folder);
end

% 模拟用户选择前5个文件
selected_files = {available_files(1:min(5, length(available_files))).name};
selected_path = raw_data_folder;

fprintf('模拟选择了 %d 个文件\n', length(selected_files));

% 显示选择的文件列表
fprintf('\n选择的文件列表:\n');
for i = 1:length(selected_files)
    file_path = fullfile(selected_path, selected_files{i});
    file_info_temp = dir(file_path);
    if ~isempty(file_info_temp)
        fprintf('  %d. %s (%.2f KB, %s)\n', i, selected_files{i}, ...
                file_info_temp.bytes/1024, file_info_temp.date);
    else
        fprintf('  %d. %s (文件不存在)\n', i, selected_files{i});
    end
end

fprintf('\n自动确认处理选择的文件...\n');

%% 验证和解析选择的文件
% 解析选择的文件名并提取排序信息
file_info = [];
invalid_files = {};
missing_files = {};

for i = 1:length(selected_files)
    filename = selected_files{i};
    file_path = fullfile(selected_path, filename);
    
    % 检查文件是否存在
    if ~exist(file_path, 'file')
        missing_files{end+1} = filename;
        continue;
    end
    
    % 使用正则表达式解析文件名: dataX_5min_segXXX_tt_yes_XX.mat
    pattern = 'data(\d+)_5min_seg(\d+)_tt_yes_(\d+)\.mat';
    tokens = regexp(filename, pattern, 'tokens');
    
    if ~isempty(tokens)
        data_num = str2double(tokens{1}{1});
        seg_num = str2double(tokens{1}{2});
        yes_num = str2double(tokens{1}{3});
        
        file_info = [file_info; struct('filename', filename, ...
                                      'filepath', file_path, ...
                                      'data_num', data_num, ...
                                      'seg_num', seg_num, ...
                                      'yes_num', yes_num, ...
                                      'sort_key', data_num * 1000 + seg_num)];
    else
        invalid_files{end+1} = filename;
    end
end

% 报告验证结果
if ~isempty(missing_files)
    fprintf('警告：以下文件不存在，将被跳过:\n');
    for i = 1:length(missing_files)
        fprintf('  - %s\n', missing_files{i});
    end
end

if ~isempty(invalid_files)
    fprintf('警告：以下文件名格式不匹配，将被跳过:\n');
    for i = 1:length(invalid_files)
        fprintf('  - %s\n', invalid_files{i});
    end
end

if isempty(file_info)
    error('没有找到符合命名格式且存在的文件，脚本退出');
end

fprintf('成功验证 %d 个文件\n', length(file_info));

% 按排序键排序
[~, sort_idx] = sort([file_info.sort_key]);
file_info = file_info(sort_idx);

% 检查段号连续性
fprintf('\n检查段号连续性...\n');
data_groups = unique([file_info.data_num]);
for data_idx = 1:length(data_groups)
    current_data_num = data_groups(data_idx);
    current_files = file_info([file_info.data_num] == current_data_num);
    seg_numbers = [current_files.seg_num];
    
    if length(seg_numbers) > 1
        expected_segs = min(seg_numbers):max(seg_numbers);
        missing_segs = setdiff(expected_segs, seg_numbers);
        
        if ~isempty(missing_segs)
            fprintf('警告：data%d 缺少以下段号: %s\n', current_data_num, ...
                    mat2str(missing_segs));
        else
            fprintf('data%d 段号连续 (seg%03d 到 seg%03d)\n', current_data_num, ...
                    min(seg_numbers), max(seg_numbers));
        end
    else
        fprintf('data%d 只有一个段: seg%03d\n', current_data_num, seg_numbers(1));
    end
end

fprintf('\n文件排序结果:\n');
for i = 1:length(file_info)
    fprintf('  %d. %s (data%d, seg%03d)\n', i, file_info(i).filename, ...
            file_info(i).data_num, file_info(i).seg_num);
end

%% 加载第一个文件以确定timetable变量
fprintf('\n正在分析数据结构...\n');
first_file_path = file_info(1).filepath;
first_data = load(first_file_path);

% 找到所有timetable变量
var_names = fieldnames(first_data);
timetable_vars = {};
for i = 1:length(var_names)
    if istimetable(first_data.(var_names{i}))
        timetable_vars{end+1} = var_names{i};
    end
end

if isempty(timetable_vars)
    error('在第一个文件中未找到timetable变量');
end

fprintf('找到 %d 个timetable变量: %s\n', length(timetable_vars), strjoin(timetable_vars, ', '));

%% 处理每个timetable变量
for var_idx = 1:length(timetable_vars)
    var_name_pattern = timetable_vars{var_idx};
    fprintf('\n=== 处理变量类型: %s ===\n', var_name_pattern);
    
    % 初始化合并数据
    merged_timetable = [];
    current_time_offset = seconds(0);
    
    % 逐个处理文件
    for file_idx = 1:length(file_info)
        file_path = file_info(file_idx).filepath;
        fprintf('处理文件 %d/%d: %s\n', file_idx, length(file_info), file_info(file_idx).filename);
        
        % 加载文件
        try
            data = load(file_path);
        catch ME
            warning('无法加载文件 %s: %s', file_info(file_idx).filename, ME.message);
            continue;
        end
        
        % 查找对应的timetable变量
        current_var_name = '';
        data_fields = fieldnames(data);
        for field_idx = 1:length(data_fields)
            field_name = data_fields{field_idx};
            if istimetable(data.(field_name))
                % 检查变量名是否匹配模式（去掉文件特定前缀）
                if contains(field_name, 'tt1') && contains(var_name_pattern, 'tt1')
                    current_var_name = field_name;
                    break;
                elseif contains(field_name, 'tt2') && contains(var_name_pattern, 'tt2')
                    current_var_name = field_name;
                    break;
                end
            end
        end
        
        if isempty(current_var_name)
            warning('在文件 %s 中未找到匹配的timetable变量', file_info(file_idx).filename);
            continue;
        end
        
        % 获取当前文件的timetable
        current_tt = data.(current_var_name);
        
        % 计算当前文件的时长
        file_duration = current_tt.Time(end) - current_tt.Time(1);
        
        % 调整时间戳
        if file_idx == 1
            % 第一个文件：将时间归零
            adjusted_time = current_tt.Time - current_tt.Time(1);
        else
            % 后续文件：在前一个文件结束时间基础上继续
            adjusted_time = current_time_offset + (current_tt.Time - current_tt.Time(1));
        end
        
        % 创建调整后的timetable
        adjusted_tt = timetable(adjusted_time, current_tt.Variables);
        adjusted_tt.Properties.VariableNames = current_tt.Properties.VariableNames;
        adjusted_tt.Properties.DimensionNames{1} = 'Time';
        
        % 合并数据
        if isempty(merged_timetable)
            merged_timetable = adjusted_tt;
        else
            merged_timetable = [merged_timetable; adjusted_tt];
        end
        
        % 更新时间偏移量
        current_time_offset = adjusted_time(end) + seconds(1/2570); % 假设采样频率2570Hz
        
        fprintf('  - 数据点: %d, 时长: %.1f秒\n', height(current_tt), seconds(file_duration));
    end
    
    % 保存合并结果
    if ~isempty(merged_timetable)
        % 生成输出文件名
        timestamp = datestr(now, 'yyyymmdd_HHMMSS');
        var_suffix = '';
        if contains(var_name_pattern, 'tt1')
            var_suffix = '_tt1';
        elseif contains(var_name_pattern, 'tt2')
            var_suffix = '_tt2';
        end
        
        output_filename = sprintf('merged_data%d_selected_segments%s_%s.mat', ...
                                 file_info(1).data_num, var_suffix, timestamp);
        output_path = fullfile(processed_data_folder, output_filename);
        
        % 创建保存变量 - 使用简化的变量名
        if contains(var_name_pattern, 'tt1')
            merged_tt1 = merged_timetable;
            save(output_path, 'merged_tt1');
        elseif contains(var_name_pattern, 'tt2')
            merged_tt2 = merged_timetable;
            save(output_path, 'merged_tt2');
        else
            merged_data = merged_timetable;
            save(output_path, 'merged_data');
        end
        
        fprintf('\n合并完成！\n');
        fprintf('输出文件: %s\n', output_filename);
        fprintf('总数据点: %d\n', height(merged_timetable));
        
        % 获取时间向量 - 使用RowTimes属性访问timetable的时间
        time_vector = merged_timetable.Properties.RowTimes;
        
        fprintf('总时长: %.1f秒\n', seconds(time_vector(end) - time_vector(1)));
        fprintf('时间范围: %s 到 %s\n', char(time_vector(1)), char(time_vector(end)));
    else
        warning('变量 %s 的合并结果为空', var_name_pattern);
    end
end

fprintf('\n=== 数据合并完成 ===\n');
