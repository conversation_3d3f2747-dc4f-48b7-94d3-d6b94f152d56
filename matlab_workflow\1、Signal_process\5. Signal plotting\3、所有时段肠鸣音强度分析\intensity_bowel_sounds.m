%% 肠鸣音强度分析程序
% 功能：基于频谱分析计算不同时间段内各类型肠鸣音的强度分布
% 输入：'全部数据'文件夹中的.mat文件（包含timetable格式的音频数据）
% 输出：控制台强度统计、Excel文件（肠鸣音强度统计结果.xlsx）、强度变化图表
% 处理步骤：
%   1. 加载音频数据并计算频谱图（100-800Hz频段）
%   2. 计算各类型肠鸣音的频域强度（SB、MB、CRS）
%   3. 按5分钟时间段累加强度值
%   4. 生成Excel报告和强度变化可视化图表
%   5. 创建10分钟间隔的整体强度趋势分析
% 应用场景：医学研究中肠鸣音信号的强度量化分析和时间变化监测
% 数据结构：按照上午/下午、第一次/第二次、5分钟时间段进行组织

clear all;
clc;
close all;

% 定义声音处理参数
w = 0.05; % 设置窗口宽度（秒）
fs = 2570; % 采样频率
win = round(fs*w); % 计算窗口大小（样本数）
ov = round(fs*w*0.9); % 计算重叠部分（样本数）
nfft = round(fs*0.5); % 设置FFT点数
freq_range = 39:311;  % 对应约100-800Hz

% 定义根目录
root_dir = '全部数据';

% 定义时间段（上午/下午）
time_periods = {'上午', '下午'};
time_periods_field = {'morning', 'afternoon'}; % 用于结构体字段名

% 定义测量次数
measurements = {'第一次', '第二次'};
measurements_field = {'first', 'second'}; % 用于结构体字段名

% 初始化结果存储结构
results = struct();

% 遍历上午和下午
for period_idx = 1:length(time_periods)
    period = time_periods{period_idx};
    period_field = time_periods_field{period_idx};
    results.(period_field) = struct();
    
    % 先获取两次测量的总文件夹数
    total_folders = 0;
    folder_counts = zeros(1, length(measurements));
    
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        current_path = fullfile(root_dir, period, meas);
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        data_folders = sort_data_folders(data_folders);  % 对文件夹进行排序
        folder_counts(meas_idx) = length(data_folders);
        total_folders = total_folders + length(data_folders);
    end
    
    % 遍历第一次和第二次
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        meas_field = measurements_field{meas_idx};
        
        % 获取当前处理路径
        current_path = fullfile(root_dir, period, meas);
        
        % 获取所有5分钟数据文件夹
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        data_folders = sort_data_folders(data_folders);  % 对文件夹进行排序
        
        % 初始化当前测量的结果数组
        n_folders = length(data_folders);
        time_results = struct('SB', zeros(total_folders, 1), ...
                            'MB', zeros(total_folders, 1), ...
                            'CRS', zeros(total_folders, 1));
        
        % 计算起始索引
        start_idx = 1;
        if meas_idx > 1
            start_idx = sum(folder_counts(1:meas_idx-1)) + 1;
        end
        
        % 遍历每个5分钟文件夹
        for folder_idx = 1:n_folders
            folder_name = data_folders(folder_idx).name;
            folder_path = fullfile(current_path, folder_name);
            
            % 计算实际的存储索引
            actual_idx = start_idx + folder_idx - 1;
            
            % 获取文件夹中的所有.mat文件
            mat_files = dir(fullfile(folder_path, '*.mat'));
            
            % 计算各类型强度
            for file_idx = 1:length(mat_files)
                file_name = mat_files(file_idx).name;
                file_path = fullfile(folder_path, file_name);
                
                % 加载声音数据
                try
                    % 加载.mat文件
                    data = load(file_path);
                    % 获取timetable中的数据
                    if isfield(data, 'extractedData') && istimetable(data.extractedData)
                        x = data.extractedData.column2;  % 提取column2列的数据
                        
                        % 计算频谱图
                        [S, F, T, P] = spectrogram(x, win, ov, nfft, fs);
                        
                        % 计算声音强度
                        I_BowelSound = sum(P(freq_range,:));
                        intensity = mean(I_BowelSound); % 取平均值作为该声音的强度
                        
                        % 根据文件名累加强度
                        if contains(file_name, '_SB_')
                            time_results.SB(actual_idx) = time_results.SB(actual_idx) + intensity;
                        elseif contains(file_name, '_MB_')
                            time_results.MB(actual_idx) = time_results.MB(actual_idx) + intensity;
                        elseif contains(file_name, '_CRS_')
                            time_results.CRS(actual_idx) = time_results.CRS(actual_idx) + intensity;
                        end
                    else
                        warning('文件 %s 中未找到正确的数据结构', file_path);
                    end
                catch ME
                    warning('处理文件时出错: %s\n错误信息: %s', file_path, ME.message);
                    continue;
                end
            end
            
            % 显示进度
            fprintf('%s %s - 处理文件夹 %s: SB=%.2f, MB=%.2f, CRS=%.2f\n', ...
                period, meas, folder_name, ...
                time_results.SB(actual_idx), ...
                time_results.MB(actual_idx), ...
                time_results.CRS(actual_idx));
        end
        
        % 保存当前测量的结果
        results.(period_field).(meas_field) = time_results;
    end
end

% 显示结果
for period_idx = 1:length(time_periods)
    period = time_periods{period_idx};
    period_field = time_periods_field{period_idx};
    fprintf('\n=== %s强度统计结果 ===\n', period);
    
    % 获取总文件夹数
    total_folders = 0;
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        current_path = fullfile(root_dir, period, meas);
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        total_folders = total_folders + length(data_folders);
    end
    
    % 显示结果
    fprintf('时间段\t\tSB\tMB\tCRS\n');
    for folder_idx = 1:total_folders
        % 计算实际的时间范围
        start_time = (folder_idx-1)*5;
        end_time = folder_idx*5;
        fprintf('%d-%.0f分钟\t%.2f\t%.2f\t%.2f\n', ...
            start_time, end_time, ...
            results.(period_field).first.SB(folder_idx) + results.(period_field).second.SB(folder_idx), ...
            results.(period_field).first.MB(folder_idx) + results.(period_field).second.MB(folder_idx), ...
            results.(period_field).first.CRS(folder_idx) + results.(period_field).second.CRS(folder_idx));
    end
end

% 保存结果到Excel文件
filename = '肠鸣音强度统计结果.xlsx';
for period_idx = 1:length(time_periods)
    period = time_periods{period_idx};
    period_field = time_periods_field{period_idx};
    
    % 获取总文件夹数
    total_folders = 0;
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        current_path = fullfile(root_dir, period, meas);
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        total_folders = total_folders + length(data_folders);
    end
    
    % 创建表格数据
    data = cell(total_folders + 1, 4);
    data(1,:) = {'时间段', 'SB强度', 'MB强度', 'CRS强度'};
    
    for folder_idx = 1:total_folders
        data{folder_idx+1, 1} = sprintf('%d-%.0f分钟', (folder_idx-1)*5, folder_idx*5);
        data{folder_idx+1, 2} = results.(period_field).first.SB(folder_idx) + results.(period_field).second.SB(folder_idx);
        data{folder_idx+1, 3} = results.(period_field).first.MB(folder_idx) + results.(period_field).second.MB(folder_idx);
        data{folder_idx+1, 4} = results.(period_field).first.CRS(folder_idx) + results.(period_field).second.CRS(folder_idx);
    end
    
    % 写入Excel
    writecell(data, filename, 'Sheet', period);
end

fprintf('\n结果已保存到文件：%s\n', filename);

% 绘制统计图表
figure('Name', '肠鸣音强度统计结果', 'Position', [************ 800]);

for period_idx = 1:length(time_periods)
    period = time_periods{period_idx};
    period_field = time_periods_field{period_idx};
    
    % 创建子图
    subplot(2, 1, period_idx);
    
    % 获取总文件夹数
    total_folders = 0;
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        current_path = fullfile(root_dir, period, meas);
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        total_folders = total_folders + length(data_folders);
    end
    
    % 准备数据
    x = 1:total_folders;
    
    % 合并第一次和第二次的数据
    combined_data = struct();
    combined_data.SB = results.(period_field).first.SB + results.(period_field).second.SB;
    combined_data.MB = results.(period_field).first.MB + results.(period_field).second.MB;
    combined_data.CRS = results.(period_field).first.CRS + results.(period_field).second.CRS;
    
    % 绘制柱状图
    hold on;
    b1 = bar(x, [combined_data.SB(1:total_folders), combined_data.MB(1:total_folders), combined_data.CRS(1:total_folders)], ...
        'grouped');
    
    % 设置颜色
    set(b1(1), 'FaceColor', 'r', 'FaceAlpha', 0.6);
    set(b1(2), 'FaceColor', 'g', 'FaceAlpha', 0.6);
    set(b1(3), 'FaceColor', 'b', 'FaceAlpha', 0.6);
    
    % 设置图表属性
    title(sprintf('%s肠鸣音强度统计', period));
    xlabel('时间段（5分钟）');
    ylabel('强度');
    
    % 设置x轴刻度
    if total_folders > 0
        xticks(1:total_folders);
        xticklabels(arrayfun(@(x) sprintf('%d-%d', (x-1)*5, x*5), 1:total_folders, 'UniformOutput', false));
        xtickangle(45);
    end
    
    % 添加图例
    legend({'SB', 'MB', 'CRS'}, 'Location', 'northeastoutside');
    
    grid on;
    hold off;
end

% 调整图表布局
set(gcf, 'Color', 'white');

% 准备数据
morning_data = struct('SB', results.morning.first.SB + results.morning.second.SB, ...
                     'MB', results.morning.first.MB + results.morning.second.MB, ...
                     'CRS', results.morning.first.CRS + results.morning.second.CRS);
afternoon_data = struct('SB', results.afternoon.first.SB + results.afternoon.second.SB, ...
                       'MB', results.afternoon.first.MB + results.afternoon.second.MB, ...
                       'CRS', results.afternoon.first.CRS + results.afternoon.second.CRS);

% 获取数据长度
morning_length = length(morning_data.SB);
afternoon_length = length(afternoon_data.SB);

% 将数据重组为10分钟间隔
morning_10min_length = floor(morning_length/2);
afternoon_10min_length = floor(afternoon_length/2);

% 创建10分钟间隔的数据数组
all_data_10min = zeros(morning_10min_length + afternoon_10min_length, 3);

% 合并上午数据为10分钟间隔
for i = 1:morning_10min_length
    idx = (i-1)*2 + 1;
    all_data_10min(i, 1) = sum(morning_data.SB(idx:min(idx+1, morning_length)));
    all_data_10min(i, 2) = sum(morning_data.MB(idx:min(idx+1, morning_length)));
    all_data_10min(i, 3) = sum(morning_data.CRS(idx:min(idx+1, morning_length)));
end

% 合并下午数据为10分钟间隔
for i = 1:afternoon_10min_length
    idx = (i-1)*2 + 1;
    all_data_10min(morning_10min_length + i, 1) = sum(afternoon_data.SB(idx:min(idx+1, afternoon_length)));
    all_data_10min(morning_10min_length + i, 2) = sum(afternoon_data.MB(idx:min(idx+1, afternoon_length)));
    all_data_10min(morning_10min_length + i, 3) = sum(afternoon_data.CRS(idx:min(idx+1, afternoon_length)));
end

% 绘制总体强度变化图
% figure('Name', '肠鸣音强度活动统计', 'Position', [200 200 1500 450]);
plot_bowel_sounds(all_data_10min, ...                    % 数据
                 morning_10min_length, ...               % 上午数据长度
                 [200 200 1500 450], ...                % 图表位置和大小
                 'Bowel Sound Intensity Over Time', ...  % 标题
                 [0.5 size(all_data_10min,1)+0.5], ...  % X轴范围
                 [0 max(all_data_10min(:))*1.1]);       % Y轴范围

% % 保存图表
% saveas(gcf, '肠鸣音强度活动统计_10min.png');
% saveas(gcf, '肠鸣音强度活动统计_10min.fig');

% 自定义排序函数
function sorted_folders = sort_data_folders(folders)
    % 提取文件夹名中的数字
    numbers = zeros(length(folders), 1);
    for i = 1:length(folders)
        % 使用正则表达式提取数字
        name = folders(i).name;
        match = regexp(name, 'data(\d+)_', 'tokens');
        if ~isempty(match)
            numbers(i) = str2double(match{1}{1});
        end
    end
    
    % 根据提取的数字排序
    [~, idx] = sort(numbers);
    sorted_folders = folders(idx);
end
