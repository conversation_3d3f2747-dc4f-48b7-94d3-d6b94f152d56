function savePreprocessedData(preprocessedSignals, originalFileName, outputFolder, config)
%SAVEPREPROCESSEDDATA 保存预处理数据转换结果
%   将经过预处理的CSV数据（去直流、去趋势、归一化）转换为时间表格式并保存，支持分割处理。
%   该函数确保预处理数据以与其他处理结果相同的格式保存，便于后续对比分析。
%
%   语法:
%   savePreprocessedData(preprocessedSignals, originalFileName, outputFolder, config)
%
%   输入参数:
%   preprocessedSignals - 预处理信号数据 (元胞数组)
%                        {column2, column3} - CSV第2列和第3列的预处理数据
%                        已经过以下处理：
%                        - 去直流分量（DC removal）
%                        - 去趋势处理（detrend）
%                        - 归一化处理（normalization）
%   originalFileName    - 原始文件名（用于生成输出文件名）
%   outputFolder        - 主输出文件夹路径
%   config             - 处理配置参数结构体
%
%   输出结果:
%   在指定文件夹的子文件夹中生成预处理数据文件：
%   - 1_preprocessed_data/: 预处理数据转换结果
%     - original_timeline/: 原始时间刻度分割文件
%     - reset_timeline/: 重置时间刻度分割文件
%
%   文件命名格式:
%   - 完整文件: [原文件名]_preprocessed_tt.mat
%   - 分割文件: [原文件名]_preprocessed_seg[序号]_tt.mat
%   每个文件包含: [文件名]_tt1 (CSV第2列), [文件名]_tt2 (CSV第3列)
%
%   配置参数:
%   config.savePreprocessedData      - 是否保存预处理数据
%   config.preprocessedDataFolder    - 预处理数据文件夹名
%   config.enableStepSegmentation    - 是否对预处理数据进行分割
%   config.samplingRate              - 采样率
%   其他分割相关配置参数...
%
%   预处理步骤说明:
%   该函数保存的数据已经过以下预处理步骤：
%   1. 去直流分量：signal = signal - mean(signal)
%   2. 去趋势处理：signal = detrend(signal)
%   3. 归一化处理：signal = signal / normalizationFactor 或 signal / max(abs(signal))
%
%   处理流程:
%   1. 验证输入参数和预处理数据
%   2. 创建预处理数据文件夹结构
%   3. 将预处理数据转换为时间表格式
%   4. 保存完整的预处理数据文件（可选）
%   5. 如果启用分割，对预处理数据进行分割处理
%   6. 生成处理报告和统计信息
%
%   示例:
%   % 基本用法
%   config = createProcessingConfig();
%   config.savePreprocessedData = true;
%   config.enableStepSegmentation = true;
%   preprocessedSignals = {preprocessed_column2, preprocessed_column3};
%   savePreprocessedData(preprocessedSignals, 'data1.csv', './output', config);
%
%   注意事项:
%   - 输入数据必须是已经过预处理的数据
%   - 分割参数与其他处理步骤保持一致
%   - 文件命名包含'preprocessed'标识
%   - 支持与其他步骤相同的双时间刻度功能
%
%   参见: SAVEALLPROCESSINGSTEPS, SEGMENTANDSAVESTEPTIMETABLE, CREATEPROCESSINGCONFIG
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    %% 参数验证
    if nargin < 4
        error('savePreprocessedData:NotEnoughInputs', '需要4个输入参数');
    end
    
    if ~config.savePreprocessedData
        if config.enableVerboseOutput
            fprintf('预处理数据保存已禁用，跳过处理\n');
        end
        return;
    end
    
    % 验证预处理数据
    if ~iscell(preprocessedSignals) || length(preprocessedSignals) < 2
        error('savePreprocessedData:InvalidPreprocessedSignals', ...
            '预处理信号数据格式错误，需要包含两个通道的元胞数组');
    end
    
    %% 创建预处理数据文件夹结构
    preprocessedDataFolder = fullfile(outputFolder, config.preprocessedDataFolder);
    
    % 创建主文件夹
    if ~exist(preprocessedDataFolder, 'dir')
        mkdir(preprocessedDataFolder);
        if config.enableVerboseOutput
            fprintf('创建预处理数据文件夹: %s\n', preprocessedDataFolder);
        end
    end
    
    %% 处理预处理数据
    if config.enableVerboseOutput
        fprintf('\n=== 保存预处理数据 ===\n');
    end
    
    try
        % 获取预处理信号数据
        column2_preprocessed = preprocessedSignals{1};
        column3_preprocessed = preprocessedSignals{2};
        
        if isempty(column2_preprocessed) || isempty(column3_preprocessed)
            warning('savePreprocessedData:EmptyPreprocessedData', ...
                '预处理数据为空，跳过处理');
            return;
        end
        
        % 验证数据长度一致性
        if length(column2_preprocessed) ~= length(column3_preprocessed)
            error('savePreprocessedData:DataLengthMismatch', ...
                '两个通道的预处理数据长度不一致');
        end
        
        % 创建时间表（数据已经过预处理）
        tt_ch2_preprocessed = timetable(column2_preprocessed, 'SampleRate', config.samplingRate);
        tt_ch3_preprocessed = timetable(column3_preprocessed, 'SampleRate', config.samplingRate);
        
        % 生成文件名
        [~, baseName, ~] = fileparts(originalFileName);
        cleanBaseName = regexprep(baseName, '[^a-zA-Z0-9_]', '_');
        if ~isempty(cleanBaseName) && ~isletter(cleanBaseName(1))
            cleanBaseName = ['file_', cleanBaseName];
        end
        
        % 保存完整的预处理数据文件（可选）
        if ~config.enableStepSegmentation
            % 只保存完整文件，不分割
            fullFileName = fullfile(preprocessedDataFolder, [cleanBaseName, '_preprocessed_tt.mat']);
            
            % 创建变量名
            var1Name = [cleanBaseName, '_preprocessed_tt1'];
            var2Name = [cleanBaseName, '_preprocessed_tt2'];
            
            % 保存文件
            eval([var1Name, ' = tt_ch2_preprocessed;']);
            eval([var2Name, ' = tt_ch3_preprocessed;']);
            save(fullFileName, var1Name, var2Name);
            
            if config.enableProgressDisplay
                fprintf('已保存预处理数据完整文件: %s\n', [cleanBaseName, '_preprocessed_tt.mat']);
            end
        else
            % 进行分割处理
            if config.enableVerboseOutput
                fprintf('对预处理数据进行分割处理...\n');
            end
            
            % 调用分割函数，使用修改后的文件名前缀
            modifiedFileName = [cleanBaseName, '_preprocessed'];
            segmentAndSaveStepTimeTable(tt_ch2_preprocessed, tt_ch3_preprocessed, modifiedFileName, ...
                preprocessedDataFolder, config, 'preprocessed');
        end
        
        if config.enableVerboseOutput
            dataLength = length(column2_preprocessed);
            dataDuration = dataLength / config.samplingRate;
            fprintf('预处理数据长度: %d 样本 (%.2f 秒)\n', dataLength, dataDuration);
            fprintf('预处理数据保存完成\n');
            fprintf('预处理步骤包括: 去直流分量、去趋势处理、归一化处理\n');
        end
        
    catch ME
        if config.enableErrorHandling
            warning('savePreprocessedData:ProcessingFailed', ...
                '预处理数据处理失败: %s', ME.message);
        else
            rethrow(ME);
        end
    end
    
    %% 输出总结信息
    if config.enableVerboseOutput
        fprintf('\n=== 预处理数据保存完成 ===\n');
        fprintf('预处理数据文件夹: %s\n', preprocessedDataFolder);
        fprintf('========================\n\n');
    end
end
