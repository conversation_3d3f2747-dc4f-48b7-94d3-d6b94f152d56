# 数据合并脚本使用说明

## 概述
`merge_data_files.m` 脚本用于将 `1、Raw data` 文件夹中的多个时间表（timetable）数据文件按照特定顺序合并成连续的时间序列。脚本支持两种运行模式：**GUI交互模式**和**批处理模式**。

## 功能特性
- **交互式文件选择**：GUI模式支持用户选择特定文件进行合并
- **批处理模式**：自动处理文件夹中的所有.mat文件
- **智能排序**：按照文件名中的数字序号和段号进行排序
- **段号连续性检查**：自动检查并警告缺失的段号
- **时间连续性**：确保合并后的时间序列连续且有序
- **多变量支持**：同时处理tt1和tt2两种类型的时间表变量
- **错误处理**：完善的文件验证和错误提示机制
- **自动保存**：将合并结果保存到Processed data文件夹

## 排序规则
1. **主排序**：按文件名中的数字序号排序（如：data3 → data4）
2. **次排序**：按文件内的段号排序（如：seg001 → seg002 → seg003 → seg004 → seg005）

## 输入要求
- **文件格式**：.mat格式文件
- **文件命名**：`dataX_5min_segXXX_tt_yes_XX.mat`
- **数据结构**：包含timetable格式的变量
- **时间格式**：duration类型的时间戳

## 输出结果
- **保存位置**：`2、Processed data` 文件夹
- **GUI模式文件命名**：`merged_dataX_selected_segments_YYYYMMDD_HHMMSS.mat`
- **批处理模式文件命名**：`merged_dataX_all_segments_YYYYMMDD_HHMMSS.mat`
- **文件内容**：单个.mat文件包含 `merged_tt1` 和 `merged_tt2` 两个变量
- **数据格式**：与原始数据文件格式相同，tt1和tt2保存在同一文件中

## 使用方法

### 方法一：GUI交互模式（推荐）

#### 1. 使用便捷启动脚本
```matlab
A_run_merge_gui
```

#### 2. 或直接调用主函数
```matlab
C_merge_data_files(true)  % 或 C_merge_data_files() 默认为GUI模式
```

#### 3. 操作步骤
1. 脚本会自动打开文件选择对话框
2. 浏览到 `1、Raw data` 文件夹
3. 使用Ctrl+点击或Shift+点击选择多个文件
4. 点击"打开"确认选择
5. 查看控制台输出的文件列表和验证信息
6. 脚本自动处理并保存结果

### 方法二：批处理模式

#### 1. 使用便捷启动脚本
```matlab
B_run_merge_batch
```

#### 2. 或直接调用主函数
```matlab
C_merge_data_files(false)
```

#### 3. 或在命令行中运行
```bash
matlab -batch "C_merge_data_files(false)"
```

### 查看结果
脚本运行完成后，合并的数据文件将保存在 `2、Processed data` 文件夹中。

## GUI模式特性详解

### 文件选择对话框功能
- **多文件选择**：支持Ctrl+点击选择多个不连续文件，Shift+点击选择连续文件范围
- **文件信息显示**：显示文件大小、修改日期等信息帮助用户判断
- **只显示.mat文件**：自动过滤，只显示相关的数据文件
- **默认路径**：自动打开到Raw data文件夹

### 智能验证功能
- **文件存在性检查**：验证选择的文件是否真实存在
- **文件名格式验证**：检查文件名是否符合预期格式
- **段号连续性分析**：自动检查并报告缺失的段号
- **错误处理**：对无效文件给出清晰的警告信息

### 用户交互体验
- **文件列表确认**：显示选择的文件列表供用户确认
- **处理进度显示**：实时显示文件处理进度
- **详细日志输出**：提供完整的处理过程信息

## 运行示例

### 输入文件列表
```
1、Raw data/
├── data3_5min_seg001_tt_yes_15.mat
├── data3_5min_seg002_tt_yes_10.mat
├── data3_5min_seg003_tt_yes_11.mat
├── data3_5min_seg004_tt_yes_14.mat
├── data3_5min_seg005_tt_yes_16.mat
├── data4_5min_seg001_tt_yes_13.mat
├── data4_5min_seg002_tt_yes_13.mat
├── data4_5min_seg003_tt_yes_11.mat
├── data4_5min_seg004_tt_yes_12.mat
└── data4_5min_seg005_tt_yes_15.mat
```

### 输出结果
```
2、Processed data/
└── merged_data3_all_segments_20250826_112724.mat  # 包含merged_tt1和merged_tt2
```

### 控制台输出示例
```
=== 数据文件合并脚本 ===
扫描文件夹: 1、Raw data
找到 10 个文件
文件排序结果:
  1. data3_5min_seg001_tt_yes_15.mat (data3, seg001)
  2. data3_5min_seg002_tt_yes_10.mat (data3, seg002)
  ...

=== 处理变量类型: data3_5min_seg001_tt1 ===
处理文件 1/10: data3_5min_seg001_tt_yes_15.mat
  - 数据点: 154200, 时长: 60.0秒
...

合并完成！
输出文件: merged_data3_all_segments_20250826_112724.mat
包含变量: merged_tt1, merged_tt2

merged_tt1 统计信息:
  - 总数据点: 1541998
  - 总时长: 600.0秒
  - 时间范围: 0秒 到 600秒

merged_tt2 统计信息:
  - 总数据点: 1541998
  - 总时长: 600.0秒
  - 时间范围: 0秒 到 600秒
```

## 数据验证

### 验证合并结果
```matlab
% 加载合并后的数据
load('2、Processed data/merged_data3_all_segments_20250826_112724.mat');

% 查看数据结构
whos  % 显示merged_tt1和merged_tt2

% 显示前几行数据
disp('merged_tt1前5行:');
disp(merged_tt1(1:5,:));
disp('merged_tt2前5行:');
disp(merged_tt2(1:5,:));

% 检查时间连续性
time_vector1 = merged_tt1.Properties.RowTimes;
time_vector2 = merged_tt2.Properties.RowTimes;
fprintf('merged_tt1时间范围: %s 到 %s (%.1f秒)\n', ...
        char(time_vector1(1)), char(time_vector1(end)), ...
        seconds(time_vector1(end) - time_vector1(1)));
fprintf('merged_tt2时间范围: %s 到 %s (%.1f秒)\n', ...
        char(time_vector2(1)), char(time_vector2(end)), ...
        seconds(time_vector2(end) - time_vector2(1)));
```

## 技术细节

### 时间处理机制
1. **第一个文件**：时间归零，从0秒开始
2. **后续文件**：在前一个文件结束时间基础上继续
3. **采样间隔**：假设采样频率为2570Hz，时间间隔约为0.000389秒

### 变量识别逻辑
- 自动识别文件中的所有timetable变量
- 根据变量名模式匹配对应的tt1或tt2类型
- 支持不同文件中变量名前缀的变化

## 注意事项
1. 确保所有输入文件的数据格式一致
2. 文件命名必须符合规范格式
3. 时间表必须包含有效的时间戳
4. 建议在处理大量数据前先进行小规模测试

## 故障排除

### 常见错误
1. **文件格式不匹配**：检查文件命名是否符合规范
2. **找不到timetable变量**：确认文件中包含正确的时间表数据
3. **时间格式错误**：检查时间戳是否为duration类型

### 调试建议
- 使用MATLAB调试器逐步执行脚本
- 检查中间变量的值和结构
- 验证单个文件的数据格式

## 版本信息
- **版本**：1.0
- **创建日期**：2025-08-26
- **兼容性**：MATLAB R2018b及以上版本
