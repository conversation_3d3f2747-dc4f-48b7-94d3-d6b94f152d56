% function snr_filtered = calculate_snr(In, filtered_signal)
%     % 输入：
%     % In - 带噪声音信号(纯净语音信号)
%     % filtered_signal - 滤波后的声音信号(纯净语音信号)
% 
%     % 计算信号的能量
%     Ps = sum(filtered_signal.^2);
% 
%     % 计算噪声的能量（带噪信号 - 滤波后的信号）
%     Pn = sum((In - filtered_signal).^2);
% 
%     % 计算滤波后的 SNR
%     snr_filtered = 10 * log10(Ps / Pn);   % 单位：dB
% 
%     % % 输出滤波后的信噪比
%     % fprintf('滤波后的 SNR = %.4f dB\n', snr_filtered);
% end


function snr_filtered = calculate_snr(filtered_signal, In)
    % 计算带噪信号与滤波后信号的信噪比(SNR)
    % 输入：
    % In - 带噪声音信号（原始语音信号）
    % filtered_signal - 滤波后的声音信号（纯净语音信号）
    
    % 将输入信号转换为列向量，确保信号维度一致
    In = In(:);
    filtered_signal = filtered_signal(:);
    
    % 计算滤波后信号的有效能量（去掉均值）
    Ps = sum((filtered_signal - mean(filtered_signal)).^2);
    
    % 计算噪声的能量（带噪信号与滤波后信号的差值）
    Pn = sum((In - filtered_signal).^2);
    
    % 计算滤波后的信噪比（以分贝为单位）
    snr_filtered = 10 * log10(Ps / Pn);
end






% function snr=SNR_singlech(I,In)
% % 计算带噪语音信号的信噪比
% % I 是纯语音信号
% % In 是带噪的语音信号(滤波后的语音)
% % 信噪比计算公式是
% % snr=10*log10(Esignal/Enoise)
% I=I(:)';                             % 把数据转为一列
% In=In(:)';
% Ps=sum((I-mean(I)).^2);              % 信号的能量
% Pn=sum((I-In).^2);                   % 噪声的能量
% snr=10*log10(Ps/Pn);                 % 信号的能量与噪声的能量之比，再求分贝值











% %% SNR计算函数
% function snr_filtered = calculate_snr(In, filtered_signal)
%     % 输入：
%     % In - 带噪声音信号
%     % filtered_signal - 滤波后的声音信号
% 
%     % 计算信号的能量
%     Ps = sum(filtered_signal.^2);
% 
%     % 计算噪声的能量（带噪信号 - 滤波后的信号）
%     Pn = sum((In - filtered_signal).^2);
% 
%     % 计算滤波后的 SNR
%     snr_filtered = 10 * log10(Ps / Pn);   % 单位：dB
% 
%     % 输出滤波后的信噪比
%     fprintf('滤波后的 SNR = %.4f dB\n', snr_filtered);
% end