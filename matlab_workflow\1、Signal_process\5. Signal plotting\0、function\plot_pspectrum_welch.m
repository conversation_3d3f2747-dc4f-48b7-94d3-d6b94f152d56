function plot_pspectrum_welch(signal, fs, position, rows, cols, index, title_text, varargin)
%PLOT_PSPECTRUM_WELCH 使用Welch方法的肠鸣音信号功率谱绘制函数
%   使用Welch方法计算并绘制信号的功率谱密度，提供对分析参数的精细控制。
%   该函数是plot_pspectrum的Welch方法版本，适用于需要自定义谱估计参数的场景。
%
%   Welch方法通过分段、加窗、平均的方式减少功率谱估计的方差，
%   在频率分辨率和方差之间提供可控的平衡。
%
%   语法:
%   plot_pspectrum_welch(signal, fs, position, rows, cols, index, title_text)
%   plot_pspectrum_welch(signal, fs, position, rows, cols, index, title_text, time_range)
%   plot_pspectrum_welch(signal, fs, position, rows, cols, index, title_text, time_range, freq_range)
%   plot_pspectrum_welch(signal, fs, position, rows, cols, index, title_text, time_range, freq_range, window_params)
%
%   输入参数:
%   signal          - 输入信号数据 (数值向量)
%   fs              - 采样频率 (标量，单位：Hz，通常为2570Hz)
%   position        - 图形窗口位置 ([left, bottom, width, height])
%   rows            - 子图行数 (正整数)
%   cols            - 子图列数 (正整数)
%   index           - 当前子图索引 (正整数)
%   title_text      - 图形标题 (字符串)
%   time_range      - 可选，时间轴范围 ([t_min, t_max]，单位：秒)
%   freq_range      - 可选，频率轴范围 ([f_min, f_max]，单位：Hz)
%   window_params   - 可选，Welch参数结构体:
%                     .window_length - 窗长度（默认：fs*0.5秒）
%                     .overlap_ratio - 重叠比例（默认：0.5）
%                     .window_type   - 窗类型（默认：'hamming'）
%                     .nfft          - FFT点数（默认：下一个2的幂）
%
%   输出参数:
%   无 - 直接生成功率谱图显示
%
%   技术参数:
%   - 默认窗长度: 0.5秒（1285个采样点 @ 2570Hz）
%   - 默认重叠率: 50%
%   - 默认窗函数: Hamming窗
%   - 默认FFT点数: 自动选择（下一个2的幂）
%   - Y轴单位: dB (分贝)
%   - 颜色: 深蓝色 RGB[56,82,151]
%
%   算法优势:
%   - 方差降低: 通过分段平均有效降低谱估计方差
%   - 参数可控: 可精确控制频率分辨率和方差平衡
%   - 经典可靠: 基于成熟的Welch理论
%   - 适应性强: 适合各种长度和特性的信号
%
%   参数选择指导:
%   - 窗长度↑ → 频率分辨率↑，方差降低效果↓
%   - 重叠率↑ → 方差降低效果↑，计算量↑
%   - 更长信号 → 可使用更长窗口获得更好分辨率
%
%   使用示例:
%   % 基本调用（使用默认Welch参数）
%   plot_pspectrum_welch(signal, 2570, [100,100,800,600], 1, 1, 1, 'Welch Power Spectrum');
%
%   % 自定义窗参数
%   params.window_length = 2570;  % 1秒窗长
%   params.overlap_ratio = 0.75;  % 75%重叠
%   params.window_type = 'hann';   % Hann窗
%   plot_pspectrum_welch(signal, 2570, [100,100,800,600], 1, 1, 1, 'Custom Welch Spectrum', [], [], params);
%
%   参见: PWELCH, PLOT_PSPECTRUM, SPECTROGRAM, PERIODOGRAM
%
%   作者: [医学信号处理团队]
%   创建日期: 2025-08-26
%   版本: 1.0

    % 输入参数验证
    if nargin < 7
        error('plot_pspectrum_welch:NotEnoughInputs', '至少需要7个输入参数');
    end

    % 验证信号数据
    if ~isnumeric(signal) || ~isvector(signal)
        error('plot_pspectrum_welch:InvalidSignal', '信号数据必须是数值向量');
    end

    % 验证采样频率
    if ~isnumeric(fs) || ~isscalar(fs) || fs <= 0
        error('plot_pspectrum_welch:InvalidSamplingRate', '采样频率必须是正数标量');
    end

    % 验证其他基本参数（简化版本，参考原函数）
    if ~isnumeric(position) || length(position) ~= 4
        error('plot_pspectrum_welch:InvalidPosition', '位置参数必须是4元素数值向量');
    end
    
    % 处理可选参数
    time_range = [];
    freq_range = [];
    window_params = struct();
    
    if nargin >= 8 && ~isempty(varargin{1})
        time_range = varargin{1};
    end

    if nargin >= 9 && ~isempty(varargin{2})
        freq_range = varargin{2};
    end
    
    if nargin >= 10 && ~isempty(varargin{3})
        window_params = varargin{3};
    end
    
    % 确保信号为列向量
    signal = signal(:);
    
    % 处理时间范围
    if ~isempty(time_range)
        start_idx = max(1, round(time_range(1) * fs) + 1);
        end_idx = min(length(signal), round(time_range(2) * fs) + 1);
        signal = signal(start_idx:end_idx);
    end
    
    % 设置默认Welch参数
    default_window_length = round(fs * 0.5);  % 0.5秒窗长
    default_overlap_ratio = 0.5;              % 50%重叠
    default_window_type = 'hamming';          % Hamming窗
    
    % 获取窗参数
    if isfield(window_params, 'window_length')
        window_length = window_params.window_length;
    else
        window_length = default_window_length;
    end
    
    if isfield(window_params, 'overlap_ratio')
        overlap_ratio = window_params.overlap_ratio;
    else
        overlap_ratio = default_overlap_ratio;
    end
    
    if isfield(window_params, 'window_type')
        window_type = window_params.window_type;
    else
        window_type = default_window_type;
    end
    
    if isfield(window_params, 'nfft')
        nfft = window_params.nfft;
    else
        nfft = max(256, 2^nextpow2(window_length));  % 自动选择FFT点数
    end
    
    % 计算重叠样本数
    noverlap = round(window_length * overlap_ratio);
    
    % 创建窗函数
    switch lower(window_type)
        case 'hamming'
            window = hamming(window_length);
        case 'hann'
            window = hann(window_length);
        case 'blackman'
            window = blackman(window_length);
        case 'bartlett'
            window = bartlett(window_length);
        otherwise
            window = hamming(window_length);
            warning('未知窗类型，使用默认Hamming窗');
    end
    
    % 使用Welch方法计算功率谱
    try
        if isempty(freq_range)
            [Pxx, F] = pwelch(signal, window, noverlap, nfft, fs);
        else
            [Pxx, F] = pwelch(signal, window, noverlap, nfft, fs);
            % 筛选频率范围
            freq_idx = F >= freq_range(1) & F <= freq_range(2);
            Pxx = Pxx(freq_idx);
            F = F(freq_idx);
        end
    catch ME
        error('plot_pspectrum_welch:WelchError', 'Welch功率谱计算失败: %s', ME.message);
    end
    
    % 创建图形
    figure('Position', position);
    subplot(rows, cols, index);
    
    % 绘制功率谱（转换为dB）
    color1 = [56/255, 82/255, 151/255]; % 深蓝色，与其他函数保持一致
    plot(F, 10*log10(Pxx), 'Color', color1, 'LineWidth', 1.5);
    
    % 设置图形属性
    ax = gca;
    ax.FontSize = 16;
    ax.FontName = 'Times New Roman';
    ax.FontWeight = 'bold';
    
    % 设置坐标轴
    if ~isempty(freq_range)
        xlim(freq_range);
    end
    xlabel('Frequency (Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Power Spectral Density (dB)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
    % 添加方法信息到标题
    method_info = sprintf('(Welch: %s窗, %.0f%%重叠)', window_type, overlap_ratio*100);
    full_title = sprintf('%s %s', title_text, method_info);
    title(full_title, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
    % 开启网格
    grid on;
    ax.GridAlpha = 0.3;
    
end
