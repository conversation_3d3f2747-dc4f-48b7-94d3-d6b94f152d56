%% 批处理模式数据合并脚本启动器
%RUN_MERGE_BATCH 启动批处理模式的数据合并脚本
%   该脚本启动批处理模式的数据合并功能，自动处理Raw data文件夹
%   中的所有.mat文件，无需用户交互。
%
%   使用方法:
%   B_run_merge_batch
%
%   功能:
%   - 自动扫描Raw data文件夹中的所有.mat文件
%   - 按照文件名规则排序
%   - 检查段号连续性
%   - 合并所有文件到单个.mat文件（包含tt1和tt2）
%
%   输出文件名格式:
%   merged_dataX_all_segments_YYYYMMDD_HHMMSS.mat
%
%   作者: [数据处理团队]
%   创建日期: 2025-08-26

fprintf('启动批处理模式数据合并脚本...\n');
C_merge_data_files(false);

