%% 测试动态时间范围功能的脚本
% 该脚本用于验证photograph.m和绘图函数的动态时间范围功能

% 清空工作空间
clear all;
close all;

% 添加函数路径
addpath('../function');

% 测试数据文件路径
test_file = '1、Raw data/data4_5min_seg005_tt_yes_15.mat';

% 检查测试文件是否存在
if ~exist(test_file, 'file')
    error('测试文件不存在: %s', test_file);
end

% 加载测试文件
fprintf('正在加载测试文件: %s\n', test_file);
load(test_file);

% 查找timetable变量
loaded_vars = who;
timetable_vars = {};
for i = 1:length(loaded_vars)
    var_name = loaded_vars{i};
    if exist(var_name, 'var') && istimetable(eval(var_name))
        timetable_vars{end+1} = var_name;
    end
end

if isempty(timetable_vars)
    error('未找到任何timetable变量');
end

% 测试每个timetable变量的时间范围
fprintf('\n=== 测试动态时间范围功能 ===\n');
for i = 1:length(timetable_vars)
    var_name = timetable_vars{i};
    tt_data = eval(var_name);
    
    % 计算时间信息
    time_normalized = tt_data.Time - tt_data.Time(1);
    time_duration_sec = seconds(time_normalized(end));
    data_points = height(tt_data);
    
    fprintf('\n变量: %s\n', var_name);
    fprintf('  数据点数: %d\n', data_points);
    fprintf('  时间长度: %.2f 秒 (%.2f 分钟)\n', time_duration_sec, time_duration_sec/60);
    fprintf('  时间范围: [0, %.2f] 秒\n', time_duration_sec);
    
    % 测试不同的时间范围设置
    fprintf('  测试时间范围设置:\n');
    
    % 1. 默认范围（数据全长）
    default_range = [0, time_duration_sec];
    fprintf('    默认范围: [%.1f, %.1f] 秒\n', default_range(1), default_range(2));
    
    % 2. 自定义范围（前半段）
    if time_duration_sec > 60
        custom_range = [0, min(60, time_duration_sec/2)];
        fprintf('    自定义范围: [%.1f, %.1f] 秒\n', custom_range(1), custom_range(2));
    end
    
    % 3. 测试刻度生成逻辑
    fprintf('    刻度生成测试:\n');
    test_ranges = {
        [0, 30],    % 30秒 - 应该每10秒一个刻度
        [0, 120],   % 2分钟 - 应该每10秒一个刻度
        [0, 300],   % 5分钟 - 应该每50秒一个刻度
        [0, 600]    % 10分钟 - 应该每60秒一个刻度
    };
    
    for j = 1:length(test_ranges)
        range = test_ranges{j};
        time_span = range(2) - range(1);
        
        if time_span <= 60
            tick_interval = 10;
        elseif time_span <= 300
            tick_interval = 50;
        else
            tick_interval = 60;
        end
        
        tick_values = range(1):tick_interval:range(2);
        fprintf('      范围[%.0f,%.0f]秒 -> 刻度间隔:%d秒 -> 刻度:[%s]\n', ...
            range(1), range(2), tick_interval, num2str(tick_values));
    end
end

% 测试绘图函数的时间范围参数
fprintf('\n=== 测试绘图函数时间范围参数 ===\n');
if ~isempty(timetable_vars)
    % 选择第一个变量进行测试
    test_var = timetable_vars{1};
    tt_test = eval(test_var);
    time_test = tt_test.Time - tt_test.Time(1);
    signal_test = tt_test.Variables;
    fs = 2570;
    
    fprintf('使用变量 %s 进行绘图函数测试\n', test_var);
    
    % 计算时间范围
    time_end = seconds(time_test(end));
    full_range = [0, time_end];
    
    fprintf('数据时间范围: [0, %.1f] 秒\n', time_end);
    
    % 测试plot_waveform函数
    fprintf('测试 plot_waveform 函数...\n');
    try
        position = [100, 100, 800, 600];
        figure('Name', 'Test Waveform - Dynamic Time Range', 'Position', position);
        plot_waveform(time_test, signal_test, position, 1, 1, 1, ...
            sprintf('Test Waveform - %s', test_var), full_range);
        fprintf('  plot_waveform 测试成功\n');
    catch ME
        fprintf('  plot_waveform 测试失败: %s\n', ME.message);
    end
    
    % 测试plot_spectrogram函数
    fprintf('测试 plot_spectrogram 函数...\n');
    try
        position = [200, 200, 800, 600];
        figure('Name', 'Test Spectrogram - Dynamic Time Range', 'Position', position);
        plot_spectrogram(signal_test, fs, position, 1, 1, 1, ...
            sprintf('Test Spectrogram - %s', test_var), full_range);
        fprintf('  plot_spectrogram 测试成功\n');
    catch ME
        fprintf('  plot_spectrogram 测试失败: %s\n', ME.message);
    end
    
    % 测试plot_intensity函数
    fprintf('测试 plot_intensity 函数...\n');
    try
        position = [300, 300, 800, 600];
        figure('Name', 'Test Intensity - Dynamic Time Range', 'Position', position);
        plot_intensity(time_test, signal_test, fs, position, 1, 1, 1, ...
            sprintf('Test Intensity - %s', test_var), full_range);
        fprintf('  plot_intensity 测试成功\n');
    catch ME
        fprintf('  plot_intensity 测试失败: %s\n', ME.message);
    end
end

fprintf('\n=== 动态时间范围功能测试完成 ===\n');
fprintf('现在可以运行 photograph.m，它将自动适应数据的实际时间长度。\n');
