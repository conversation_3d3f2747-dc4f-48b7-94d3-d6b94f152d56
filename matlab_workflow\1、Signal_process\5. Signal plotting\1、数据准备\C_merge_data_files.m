%% 数据文件合并脚本
%MERGE_DATA_FILES 将多个时间表数据文件按顺序合并为一个连续的时间序列
%   该脚本用于处理Raw data文件夹中的多个.mat文件，按照文件名中的数字序号
%   和段号进行排序，然后将时间表数据合并为一个连续的时间序列。
%
%   功能特性:
%   - 交互式文件选择界面（GUI模式）或自动处理所有文件（批处理模式）
%   - 智能文件名解析和排序（按data序号和seg段号）
%   - 时间戳连续性处理，确保合并后时间序列连续
%   - 支持多个timetable变量的批量处理
%   - 段号连续性检查和警告
%   - 自动保存合并结果到Processed data文件夹
%
%   使用方法:
%   merge_data_files          % GUI模式，交互式选择文件
%   merge_data_files(false)   % 批处理模式，处理所有文件
%
%   排序规则:
%   1. 首先按文件名中的数字序号排序（如：data3 → data4）
%   2. 然后按文件内的段号排序（如：seg001 → seg002 → seg003 → seg004 → seg005）
%
%   数据要求:
%   - 输入文件: .mat格式，包含timetable变量
%   - 文件命名格式: dataX_5min_segXXX_tt_yes_XX.mat
%   - 时间表格式: 包含Time列和数据列
%
%   输出:
%   - 合并后的时间表文件保存在2、Processed data文件夹
%   - 文件名格式: merged_dataX_selected_segments_YYYYMMDD_HHMMSS.mat (GUI模式)
%   - 文件名格式: merged_dataX_all_segments_YYYYMMDD_HHMMSS.mat (批处理模式)
%
%   作者: [数据处理团队]
%   创建日期: 2025-08-26
%   版本: 2.0 - 添加交互式文件选择功能

function merge_data_files(use_gui_mode)
    % 参数处理
    if nargin < 1
        use_gui_mode = true; % 默认使用GUI模式
    end

%% 初始化
close all;
clc;

% 设置路径
raw_data_folder = '1、Raw data';
processed_data_folder = '2、Processed data';

% 确保输出文件夹存在
if ~exist(processed_data_folder, 'dir')
    mkdir(processed_data_folder);
end

fprintf('=== 数据文件合并脚本 ===\n');

%% 文件选择逻辑
if use_gui_mode
    %% 交互式文件选择（GUI模式）
    fprintf('正在打开文件选择对话框...\n');

    % 设置文件选择对话框的初始路径
    if exist(raw_data_folder, 'dir')
        initial_path = raw_data_folder;
    else
        initial_path = pwd;
        warning('Raw data文件夹不存在，使用当前目录: %s', initial_path);
    end

    % 使用uigetfile进行多文件选择
    [selected_files, selected_path] = uigetfile(...
        '*.mat', ...
        '请选择要合并的数据文件', ...
        initial_path, ...
        'MultiSelect', 'on');

    % 检查用户是否取消了文件选择
    if isequal(selected_files, 0)
        fprintf('用户取消了文件选择，脚本退出。\n');
        return;
    end

    % 处理单个文件选择的情况（转换为cell数组）
    if ischar(selected_files)
        selected_files = {selected_files};
    end

    fprintf('用户选择了 %d 个文件\n', length(selected_files));

    % 显示选择的文件列表
    fprintf('\n选择的文件列表:\n');
    for i = 1:length(selected_files)
        file_path = fullfile(selected_path, selected_files{i});
        file_info_temp = dir(file_path);
        if ~isempty(file_info_temp)
            fprintf('  %d. %s (%.2f KB, %s)\n', i, selected_files{i}, ...
                    file_info_temp.bytes/1024, file_info_temp.date);
        else
            fprintf('  %d. %s (文件不存在)\n', i, selected_files{i});
        end
    end

    fprintf('\n自动确认处理选择的文件...\n');

else
    %% 批处理模式 - 自动处理所有文件
    fprintf('批处理模式：自动处理所有文件\n');
    fprintf('扫描文件夹: %s\n', raw_data_folder);

    % 获取所有.mat文件
    file_pattern = fullfile(raw_data_folder, '*.mat');
    file_list = dir(file_pattern);

    if isempty(file_list)
        error('在%s文件夹中未找到任何.mat文件', raw_data_folder);
    end

    selected_files = {file_list.name};
    selected_path = raw_data_folder;

    fprintf('找到 %d 个文件\n', length(selected_files));
end

% 解析选择的文件名并提取排序信息
file_info = [];
invalid_files = {};
missing_files = {};

for i = 1:length(selected_files)
    filename = selected_files{i};
    file_path = fullfile(selected_path, filename);

    % 检查文件是否存在
    if ~exist(file_path, 'file')
        missing_files{end+1} = filename;
        continue;
    end

    % 使用正则表达式解析文件名: dataX_5min_segXXX_tt_yes_XX.mat
    pattern = 'data(\d+)_5min_seg(\d+)_tt_yes_(\d+)\.mat';
    tokens = regexp(filename, pattern, 'tokens');

    if ~isempty(tokens)
        data_num = str2double(tokens{1}{1});
        seg_num = str2double(tokens{1}{2});
        yes_num = str2double(tokens{1}{3});

        file_info = [file_info; struct('filename', filename, ...
                                      'filepath', file_path, ...
                                      'data_num', data_num, ...
                                      'seg_num', seg_num, ...
                                      'yes_num', yes_num, ...
                                      'sort_key', data_num * 1000 + seg_num)];
    else
        invalid_files{end+1} = filename;
    end
end

% 报告验证结果
if ~isempty(missing_files)
    fprintf('警告：以下文件不存在，将被跳过:\n');
    for i = 1:length(missing_files)
        fprintf('  - %s\n', missing_files{i});
    end
end

if ~isempty(invalid_files)
    fprintf('警告：以下文件名格式不匹配，将被跳过:\n');
    for i = 1:length(invalid_files)
        fprintf('  - %s\n', invalid_files{i});
    end
end

if isempty(file_info)
    error('没有找到符合命名格式且存在的文件，脚本退出');
end

fprintf('成功验证 %d 个文件\n', length(file_info));

% 按排序键排序
[~, sort_idx] = sort([file_info.sort_key]);
file_info = file_info(sort_idx);

% 检查段号连续性
fprintf('\n检查段号连续性...\n');
data_groups = unique([file_info.data_num]);
for data_idx = 1:length(data_groups)
    current_data_num = data_groups(data_idx);
    current_files = file_info([file_info.data_num] == current_data_num);
    seg_numbers = [current_files.seg_num];

    if length(seg_numbers) > 1
        expected_segs = min(seg_numbers):max(seg_numbers);
        missing_segs = setdiff(expected_segs, seg_numbers);

        if ~isempty(missing_segs)
            fprintf('警告：data%d 缺少以下段号: %s\n', current_data_num, ...
                    mat2str(missing_segs));
        else
            fprintf('data%d 段号连续 (seg%03d 到 seg%03d)\n', current_data_num, ...
                    min(seg_numbers), max(seg_numbers));
        end
    else
        fprintf('data%d 只有一个段: seg%03d\n', current_data_num, seg_numbers(1));
    end
end

fprintf('\n文件排序结果:\n');
for i = 1:length(file_info)
    fprintf('  %d. %s (data%d, seg%03d)\n', i, file_info(i).filename, ...
            file_info(i).data_num, file_info(i).seg_num);
end

%% 加载第一个文件以确定timetable变量
fprintf('\n正在分析数据结构...\n');
first_file_path = file_info(1).filepath;
first_data = load(first_file_path);

% 找到所有timetable变量
var_names = fieldnames(first_data);
timetable_vars = {};
for i = 1:length(var_names)
    if istimetable(first_data.(var_names{i}))
        timetable_vars{end+1} = var_names{i};
    end
end

if isempty(timetable_vars)
    error('在第一个文件中未找到timetable变量');
end

fprintf('找到 %d 个timetable变量: %s\n', length(timetable_vars), strjoin(timetable_vars, ', '));

%% 同时处理所有timetable变量，保存在同一个文件中
fprintf('\n=== 开始合并所有变量 ===\n');

% 初始化合并数据存储
merged_data_container = struct();

% 首先计算所有文件的时间偏移量
time_offsets = cell(length(file_info), 1);
for file_idx = 1:length(file_info)
    if file_idx == 1
        time_offsets{file_idx} = seconds(0);
    else
        % 加载前一个文件来计算时间偏移
        prev_file_path = file_info(file_idx-1).filepath;
        prev_data = load(prev_file_path);
        prev_var_names = fieldnames(prev_data);

        % 找到第一个timetable变量来计算时长
        for i = 1:length(prev_var_names)
            if istimetable(prev_data.(prev_var_names{i}))
                prev_tt = prev_data.(prev_var_names{i});
                file_duration = prev_tt.Time(end) - prev_tt.Time(1);
                time_offsets{file_idx} = time_offsets{file_idx-1} + file_duration + seconds(1/2570);
                break;
            end
        end
    end
end

% 逐个处理文件
for file_idx = 1:length(file_info)
    file_path = file_info(file_idx).filepath;
    fprintf('处理文件 %d/%d: %s\n', file_idx, length(file_info), file_info(file_idx).filename);

    % 加载文件
    try
        data = load(file_path);
    catch ME
        warning('无法加载文件 %s: %s', file_info(file_idx).filename, ME.message);
        continue;
    end

    % 处理每个timetable变量
    for var_idx = 1:length(timetable_vars)
        var_name_pattern = timetable_vars{var_idx};

        % 查找对应的timetable变量
        current_var_name = '';
        data_fields = fieldnames(data);
        for field_idx = 1:length(data_fields)
            field_name = data_fields{field_idx};
            if istimetable(data.(field_name))
                % 检查变量名是否匹配模式
                if contains(field_name, 'tt1') && contains(var_name_pattern, 'tt1')
                    current_var_name = field_name;
                    break;
                elseif contains(field_name, 'tt2') && contains(var_name_pattern, 'tt2')
                    current_var_name = field_name;
                    break;
                end
            end
        end

        if isempty(current_var_name)
            warning('在文件 %s 中未找到匹配的timetable变量 %s', file_info(file_idx).filename, var_name_pattern);
            continue;
        end

        % 获取当前文件的timetable
        current_tt = data.(current_var_name);

        % 计算当前文件的时长
        file_duration = current_tt.Time(end) - current_tt.Time(1);

        % 调整时间戳（所有变量使用相同的时间基准）
        current_time_offset = time_offsets{file_idx};
        adjusted_time = current_time_offset + (current_tt.Time - current_tt.Time(1));

        % 创建调整后的timetable
        adjusted_tt = timetable(adjusted_time, current_tt.Variables);
        adjusted_tt.Properties.VariableNames = current_tt.Properties.VariableNames;
        adjusted_tt.Properties.DimensionNames{1} = 'Time';

        % 确定合并变量名
        if contains(var_name_pattern, 'tt1')
            merge_var_name = 'merged_tt1';
        elseif contains(var_name_pattern, 'tt2')
            merge_var_name = 'merged_tt2';
        else
            merge_var_name = 'merged_data';
        end

        % 合并数据
        if ~isfield(merged_data_container, merge_var_name)
            merged_data_container.(merge_var_name) = adjusted_tt;
        else
            merged_data_container.(merge_var_name) = [merged_data_container.(merge_var_name); adjusted_tt];
        end

        % 只在处理第一个变量时显示文件信息
        if var_idx == 1
            fprintf('  - 数据点: %d, 时长: %.1f秒\n', height(current_tt), seconds(file_duration));
        end
    end
end

% 保存合并结果到单个文件
if ~isempty(fieldnames(merged_data_container))
    % 生成输出文件名
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');

    % 根据模式生成不同的文件名
    if use_gui_mode
        output_filename = sprintf('merged_data%d_selected_segments_%s.mat', ...
                                 file_info(1).data_num, timestamp);
    else
        output_filename = sprintf('merged_data%d_all_segments_%s.mat', ...
                                 file_info(1).data_num, timestamp);
    end
    output_path = fullfile(processed_data_folder, output_filename);

    % 保存所有合并的变量到同一个文件
    merge_var_names = fieldnames(merged_data_container);
    save_vars = {};
    for i = 1:length(merge_var_names)
        var_name = merge_var_names{i};
        eval(sprintf('%s = merged_data_container.%s;', var_name, var_name));
        save_vars{end+1} = var_name;
    end

    % 保存文件
    save(output_path, save_vars{:});

    fprintf('\n=== 合并完成！===\n');
    fprintf('输出文件: %s\n', output_filename);
    fprintf('包含变量: %s\n', strjoin(save_vars, ', '));

    % 显示每个变量的统计信息
    for i = 1:length(save_vars)
        var_name = save_vars{i};
        merged_timetable = merged_data_container.(var_name);
        time_vector = merged_timetable.Properties.RowTimes;

        fprintf('\n%s 统计信息:\n', var_name);
        fprintf('  - 总数据点: %d\n', height(merged_timetable));
        fprintf('  - 总时长: %.1f秒\n', seconds(time_vector(end) - time_vector(1)));
        fprintf('  - 时间范围: %s 到 %s\n', char(time_vector(1)), char(time_vector(end)));
    end
else
    warning('没有成功合并任何数据');
end

fprintf('\n=== 数据合并完成 ===\n');

end % function merge_data_files
