%% 双时间刻度功能测试脚本
% =========================================================================
% 功能：测试和演示双时间刻度保存功能
% 
% 该脚本展示如何使用双时间刻度功能，包括：
% 1. 配置双时间刻度参数
% 2. 生成测试数据
% 3. 执行分割和保存
% 4. 验证结果
%
% 输出：
% - original_timeline/: 保持原始连续时间刻度的分割文件
% - reset_timeline/: 每个文件从0开始重新计时的分割文件
% =========================================================================

clear all;
clc;
close all;

%% 添加函数路径
currentDir = fileparts(mfilename('fullpath'));
functionDir = fullfile(currentDir, '0、function', '2、Spectral Subtraction');
if exist(functionDir, 'dir')
    addpath(functionDir);
    fprintf('✓ 成功添加函数路径: %s\n', functionDir);
else
    error('函数文件夹不存在: %s', functionDir);
end

%% 配置双时间刻度参数
config = createProcessingConfig();

% 启用双时间刻度功能
config.enableDualTimeScale = true;
config.saveOriginalTimeline = true;
config.saveResetTimeline = true;

% 设置分割参数（用于测试）
config.secondarySegmentLength = 30;  % 30秒片段，便于测试
config.secondaryOverlapRatio = 0.0;  % 无重叠
config.minSegmentLength = 10;        % 最小10秒

% 启用详细输出
config.enableVerboseOutput = true;
config.enableProgressDisplay = true;

fprintf('=== 双时间刻度功能测试 ===\n\n');

%% 生成测试数据（模拟5分钟的双通道信号）
fprintf('生成测试数据...\n');

samplingRate = config.samplingRate;  % 2570 Hz
duration = 300;  % 5分钟 = 300秒
numSamples = duration * samplingRate;  % 771,000样本

% 生成模拟信号
t = (0:numSamples-1)' / samplingRate;  % 时间向量

% 通道1：包含多个频率成分的信号
signal1 = 0.5 * sin(2*pi*50*t) + ...      % 50Hz主频
          0.3 * sin(2*pi*150*t) + ...     % 150Hz成分
          0.2 * sin(2*pi*300*t) + ...     % 300Hz成分
          0.1 * randn(size(t));           % 噪声

% 通道2：相似但相位不同的信号
signal2 = 0.4 * sin(2*pi*60*t + pi/4) + ...   % 60Hz主频，相位偏移
          0.3 * sin(2*pi*180*t + pi/3) + ...  % 180Hz成分
          0.2 * sin(2*pi*250*t + pi/6) + ...  % 250Hz成分
          0.1 * randn(size(t));               % 噪声

% 创建时间表
tt1 = timetable(signal1, 'SampleRate', samplingRate);
tt2 = timetable(signal2, 'SampleRate', samplingRate);

fprintf('测试数据生成完成：\n');
fprintf('  - 信号长度: %.2f秒 (%d样本)\n', duration, numSamples);
fprintf('  - 采样率: %d Hz\n', samplingRate);
fprintf('  - 通道数: 2\n\n');

%% 创建测试输出文件夹
testOutputFolder = fullfile(currentDir, 'test_output');
if ~exist(testOutputFolder, 'dir')
    mkdir(testOutputFolder);
    fprintf('创建测试输出文件夹: %s\n', testOutputFolder);
end

%% 执行双时间刻度分割
fprintf('开始执行双时间刻度分割...\n\n');

testFileName = 'test_5min_data.csv';
segmentAndSaveTimeTable(tt1, tt2, testFileName, testOutputFolder, config);

%% 验证结果
fprintf('验证分割结果...\n');

% 检查文件夹是否创建
originalFolder = fullfile(testOutputFolder, config.originalTimelineFolder);
resetFolder = fullfile(testOutputFolder, config.resetTimelineFolder);

if exist(originalFolder, 'dir')
    fprintf('✓ 原始时间刻度文件夹已创建: %s\n', originalFolder);
    originalFiles = dir(fullfile(originalFolder, '*.mat'));
    fprintf('  包含 %d 个文件\n', length(originalFiles));
else
    fprintf('✗ 原始时间刻度文件夹未找到\n');
end

if exist(resetFolder, 'dir')
    fprintf('✓ 重置时间刻度文件夹已创建: %s\n', resetFolder);
    resetFiles = dir(fullfile(resetFolder, '*.mat'));
    fprintf('  包含 %d 个文件\n', length(resetFiles));
else
    fprintf('✗ 重置时间刻度文件夹未找到\n');
end

%% 详细验证第一个片段的时间刻度
if exist(originalFolder, 'dir') && exist(resetFolder, 'dir') && ...
   length(originalFiles) > 0 && length(resetFiles) > 0
    
    fprintf('\n验证第一个片段的时间刻度差异...\n');
    
    % 加载原始时间刻度文件
    originalFile = fullfile(originalFolder, originalFiles(1).name);
    originalData = load(originalFile);
    originalVarNames = fieldnames(originalData);
    originalTT1 = originalData.(originalVarNames{1});
    
    % 加载重置时间刻度文件
    resetFile = fullfile(resetFolder, resetFiles(1).name);
    resetData = load(resetFile);
    resetVarNames = fieldnames(resetData);
    resetTT1 = resetData.(resetVarNames{1});
    
    % 显示时间刻度信息
    fprintf('原始时间刻度片段:\n');
    fprintf('  起始时间: %.3f秒\n', seconds(originalTT1.Time(1)));
    fprintf('  结束时间: %.3f秒\n', seconds(originalTT1.Time(end)));
    fprintf('  持续时间: %.3f秒\n', seconds(originalTT1.Time(end) - originalTT1.Time(1)));
    
    fprintf('重置时间刻度片段:\n');
    fprintf('  起始时间: %.3f秒\n', seconds(resetTT1.Time(1)));
    fprintf('  结束时间: %.3f秒\n', seconds(resetTT1.Time(end)));
    fprintf('  持续时间: %.3f秒\n', seconds(resetTT1.Time(end) - resetTT1.Time(1)));
    
    % 验证数据内容是否相同
    dataMatch = isequal(originalTT1.Variables, resetTT1.Variables);
    fprintf('数据内容匹配: %s\n', char(dataMatch * "是" + ~dataMatch * "否"));
    
    if dataMatch
        fprintf('✓ 双时间刻度功能工作正常！\n');
    else
        fprintf('✗ 数据内容不匹配，请检查实现\n');
    end
end

%% 清理路径
if exist('functionDir', 'var') && exist(functionDir, 'dir')
    rmpath(functionDir);
    fprintf('\n✓ 已清理函数路径\n');
end

fprintf('\n=== 双时间刻度功能测试完成 ===\n');
