function saveAllProcessingSteps(originalSignals, intermediateResults, processedSignals, originalFileName, outputFolder, config)
%SAVEALLPROCESSINGSTEPS 保存所有处理步骤的结果并进行分割
%   按照处理顺序保存原始数据、带通滤波、谱减法和最终结果的所有步骤。
%   该函数实现了完整的分步保存策略，支持双时间刻度保存和统一的文件组织结构。
%
%   语法:
%   saveAllProcessingSteps(originalSignals, intermediateResults, processedSignals, originalFileName, outputFolder, config)
%
%   输入参数:
%   originalSignals    - 原始信号数据 (元胞数组) {column2, column3}
%   intermediateResults - 中间结果结构体，包含:
%                        .bandpassFiltered - 带通滤波结果 (元胞数组)
%                        .spectralSubtracted - 谱减法结果 (元胞数组)
%   processedSignals   - 最终处理结果 (元胞数组) {column2, column3}
%   originalFileName   - 原始文件名（用于生成输出文件名）
%   outputFolder       - 主输出文件夹路径
%   config            - 处理配置参数结构体
%
%   输出结果:
%   在指定文件夹的子文件夹中生成中间结果文件：
%   - bandpass_results/: 带通滤波结果
%   - spectral_results/: 谱减法结果
%   每个子文件夹内根据配置进一步分为：
%   - original_timeline/: 原始时间刻度分割文件
%   - reset_timeline/: 重置时间刻度分割文件
%
%   文件命名格式:
%   - 完整文件: [原文件名]_bandpass_tt.mat, [原文件名]_spectral_tt.mat
%   - 分割文件: [原文件名]_bandpass_seg[序号]_tt.mat, [原文件名]_spectral_seg[序号]_tt.mat
%
%   配置参数:
%   config.saveIntermediateSteps      - 是否保存中间步骤
%   config.bandpassResultsFolder     - 带通滤波结果文件夹名
%   config.spectralResultsFolder     - 谱减法结果文件夹名
%   config.enableIntermediateSegmentation - 是否对中间结果进行分割
%   config.samplingRate              - 采样率
%   其他分割相关配置参数...
%
%   处理流程:
%   1. 验证输入参数和中间结果
%   2. 创建中间结果文件夹结构
%   3. 将中间结果转换为时间表格式
%   4. 保存完整的中间结果文件
%   5. 如果启用分割，对中间结果进行分割处理
%   6. 生成处理报告和统计信息
%
%   示例:
%   % 基本用法
%   config = createProcessingConfig();
%   config.saveIntermediateSteps = true;
%   config.enableIntermediateSegmentation = true;
%   saveIntermediateResults(intermediateResults, 'data1.csv', './output', config);
%
%   注意事项:
%   - 确保中间结果数据格式正确
%   - 中间结果文件夹会自动创建
%   - 分割参数与主处理流程保持一致
%   - 文件命名避免与主结果文件冲突
%
%   参见: PROCESSDUALCHANNELSIGNALS, SEGMENTANDSAVETIMETABLE, CREATEPROCESSINGCONFIG
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    %% 参数验证
    if nargin < 4
        error('saveIntermediateResults:NotEnoughInputs', '需要4个输入参数');
    end
    
    if ~config.saveIntermediateSteps
        if config.enableVerboseOutput
            fprintf('中间结果保存已禁用，跳过处理\n');
        end
        return;
    end
    
    % 验证中间结果数据
    if ~isfield(intermediateResults, 'bandpassFiltered') || ...
       ~isfield(intermediateResults, 'spectralSubtracted')
        error('saveIntermediateResults:InvalidIntermediateResults', ...
            '中间结果结构体缺少必要字段');
    end
    
    %% 创建中间结果文件夹结构
    bandpassFolder = fullfile(outputFolder, config.bandpassResultsFolder);
    spectralFolder = fullfile(outputFolder, config.spectralResultsFolder);
    
    % 创建主文件夹
    if ~exist(bandpassFolder, 'dir')
        mkdir(bandpassFolder);
        if config.enableVerboseOutput
            fprintf('创建带通滤波结果文件夹: %s\n', bandpassFolder);
        end
    end
    
    if ~exist(spectralFolder, 'dir')
        mkdir(spectralFolder);
        if config.enableVerboseOutput
            fprintf('创建谱减法结果文件夹: %s\n', spectralFolder);
        end
    end
    
    %% 处理带通滤波结果
    if config.enableVerboseOutput
        fprintf('\n=== 保存带通滤波中间结果 ===\n');
    end
    
    processIntermediateResult(intermediateResults.bandpassFiltered, ...
        originalFileName, bandpassFolder, config, 'bandpass');
    
    %% 处理谱减法结果
    if config.enableVerboseOutput
        fprintf('\n=== 保存谱减法中间结果 ===\n');
    end
    
    processIntermediateResult(intermediateResults.spectralSubtracted, ...
        originalFileName, spectralFolder, config, 'spectral');
    
    %% 输出总结信息
    if config.enableVerboseOutput
        fprintf('\n=== 中间结果保存完成 ===\n');
        fprintf('带通滤波结果文件夹: %s\n', bandpassFolder);
        fprintf('谱减法结果文件夹: %s\n', spectralFolder);
        fprintf('========================\n\n');
    end
end

function processIntermediateResult(signalData, originalFileName, outputFolder, config, resultType)
%PROCESSINTERMEDIATERESULT 处理单个中间结果类型
%   对指定类型的中间结果进行保存和分割处理
%
%   输入参数:
%   signalData      - 信号数据 (元胞数组)
%   originalFileName - 原始文件名
%   outputFolder    - 输出文件夹
%   config         - 配置参数
%   resultType     - 结果类型 ('bandpass' 或 'spectral')

    try
        % 验证信号数据
        if isempty(signalData) || ~iscell(signalData)
            warning('saveIntermediateResults:InvalidSignalData', ...
                '%s结果数据无效，跳过处理', resultType);
            return;
        end
        
        % 获取信号数据
        column2 = signalData{1};
        column3 = signalData{2};
        
        if isempty(column2) || isempty(column3)
            warning('saveIntermediateResults:EmptySignalData', ...
                '%s结果数据为空，跳过处理', resultType);
            return;
        end
        
        % 生成时间序列
        time = (0:length(column2)-1)' / config.samplingRate;
        
        % 创建时间表
        tt_ch2 = timetable(column2, 'SampleRate', config.samplingRate);
        tt_ch3 = timetable(column3, 'SampleRate', config.samplingRate);
        
        % 生成文件名
        [~, baseName, ~] = fileparts(originalFileName);
        cleanBaseName = regexprep(baseName, '[^a-zA-Z0-9_]', '_');
        if ~isempty(cleanBaseName) && ~isletter(cleanBaseName(1))
            cleanBaseName = ['file_', cleanBaseName];
        end
        
        % 保存完整的中间结果文件
        if ~config.enableIntermediateSegmentation
            % 只保存完整文件，不分割
            fullFileName = fullfile(outputFolder, [cleanBaseName, '_', resultType, config.intermediateFilenameSuffix, '.mat']);
            
            % 创建变量名
            var1Name = [cleanBaseName, '_', resultType, '_tt1'];
            var2Name = [cleanBaseName, '_', resultType, '_tt2'];
            
            % 保存文件
            eval([var1Name, ' = tt_ch2;']);
            eval([var2Name, ' = tt_ch3;']);
            save(fullFileName, var1Name, var2Name);
            
            if config.enableProgressDisplay
                fprintf('已保存%s完整结果: %s\n', resultType, [cleanBaseName, '_', resultType, config.intermediateFilenameSuffix, '.mat']);
            end
        else
            % 进行分割处理
            if config.enableVerboseOutput
                fprintf('对%s结果进行分割处理...\n', resultType);
            end
            
            % 调用分割函数，使用修改后的文件名前缀
            modifiedFileName = [cleanBaseName, '_', resultType];
            segmentAndSaveIntermediateTimeTable(tt_ch2, tt_ch3, modifiedFileName, outputFolder, config, resultType);
        end
        
    catch ME
        if config.enableErrorHandling
            warning('saveIntermediateResults:ProcessingFailed', ...
                '%s结果处理失败: %s', resultType, ME.message);
        else
            rethrow(ME);
        end
    end
end
