function segmentAndSaveStepTimeTable(tt_ch2, tt_ch3, baseFileName, outputFolder, config, stepType)
%SEGMENTANDSAVESTEPTIMETABLE 分割并保存各处理步骤的时间表
%   通用函数，用于处理所有处理步骤（原始数据、带通滤波、谱减法、最终结果）的分割和保存。
%   该函数统一了各步骤的分割逻辑，确保一致的文件结构和命名规范。
%
%   语法:
%   segmentAndSaveStepTimeTable(tt_ch2, tt_ch3, baseFileName, outputFolder, config, stepType)
%
%   输入参数:
%   tt_ch2       - CSV第2列的时间表数据
%   tt_ch3       - CSV第3列的时间表数据
%   baseFileName - 基础文件名（已包含步骤类型）
%   outputFolder - 输出文件夹路径
%   config       - 处理配置参数结构体
%   stepType     - 步骤类型标识 ('original', 'bandpass', 'spectral', 'final')
%
%   输出结果:
%   根据配置在指定文件夹的子文件夹中生成分割文件：
%   - original_timeline/: 保持原始连续时间刻度的分割文件
%   - reset_timeline/: 每个文件从0开始重新计时的分割文件
%   文件命名格式: [baseFileName]_seg[序号]_tt.mat
%   每个文件包含: [baseFileName]_seg[序号]_tt1, [baseFileName]_seg[序号]_tt2
%
%   步骤类型说明:
%   - 'original': 原始CSV数据转换结果
%   - 'bandpass': 带通滤波处理结果
%   - 'spectral': 谱减法处理结果
%   - 'final': 最终完整处理结果
%
%   配置参数:
%   config.secondarySegmentLength - 二次分割长度（秒）
%   config.secondaryOverlapRatio  - 重叠比例（0-0.5）
%   config.minSegmentLength      - 最小片段长度（秒）
%   config.samplingRate          - 采样率（Hz）
%   config.enableDualTimeScale   - 启用双时间刻度保存
%   config.originalTimelineFolder - 原始时间刻度文件夹名
%   config.resetTimelineFolder   - 重置时间刻度文件夹名
%   config.saveOriginalTimeline  - 保存原始连续时间刻度
%   config.saveResetTimeline     - 保存重置时间刻度
%
%   处理流程:
%   1. 参数验证和分割计算
%   2. 创建双时间刻度文件夹结构
%   3. 按配置进行时间表分割
%   4. 保存分割后的文件到相应文件夹
%   5. 生成处理报告
%
%   示例:
%   % 保存原始数据分割结果
%   segmentAndSaveStepTimeTable(tt_ch2, tt_ch3, 'data1_original', 
%                              './1_original_data', config, 'original');
%
%   % 保存带通滤波分割结果
%   segmentAndSaveStepTimeTable(tt_ch2, tt_ch3, 'data1_bandpass', 
%                              './2_bandpass_results', config, 'bandpass');
%
%   注意事项:
%   - 分割参数在所有步骤中保持一致
%   - 文件命名包含步骤类型标识
%   - 支持统一的双时间刻度功能
%   - 自动处理文件夹创建和错误处理
%
%   参见: SEGMENTANDSAVETIMETABLE, SAVEORIGINALDATA, SAVEALLPROCESSINGSTEPS
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    %% 参数验证和初始化
    if nargin < 6
        error('segmentAndSaveStepTimeTable:NotEnoughInputs', '需要6个输入参数');
    end
    
    % 验证时间表数据
    if height(tt_ch2) ~= height(tt_ch3)
        error('segmentAndSaveStepTimeTable:DimensionMismatch', '两个通道的时间表长度不匹配');
    end

    % 获取基本参数
    totalSamples = height(tt_ch2);
    samplingRate = config.samplingRate;
    totalDuration = totalSamples / samplingRate;
    
    % 计算分割参数
    segmentLengthSamples = round(config.secondarySegmentLength * samplingRate);
    overlapSamples = round(segmentLengthSamples * config.secondaryOverlapRatio);
    stepSamples = segmentLengthSamples - overlapSamples;
    minSegmentSamples = round(config.minSegmentLength * samplingRate);
    
    % 计算总的片段数量
    if totalSamples <= segmentLengthSamples
        numSegments = 1;
    else
        numCompleteSegments = floor((totalSamples - segmentLengthSamples) / stepSamples) + 1;
        lastSegmentStart = (numCompleteSegments - 1) * stepSamples + 1;
        remainingSamples = totalSamples - lastSegmentStart + 1;

        if remainingSamples > segmentLengthSamples
            numSegments = numCompleteSegments + 1;
        else
            numSegments = numCompleteSegments;
        end
    end
    
    % 清理基础文件名
    cleanBaseName = regexprep(baseFileName, '[^a-zA-Z0-9_]', '_');
    if ~isempty(cleanBaseName) && ~isletter(cleanBaseName(1))
        cleanBaseName = ['file_', cleanBaseName];
    end

    %% 创建双时间刻度文件夹
    originalTimelineFolder = '';
    resetTimelineFolder = '';

    if config.enableDualTimeScale
        if config.saveOriginalTimeline
            originalTimelineFolder = fullfile(outputFolder, config.originalTimelineFolder);
            if ~exist(originalTimelineFolder, 'dir')
                mkdir(originalTimelineFolder);
                if config.enableVerboseOutput
                    fprintf('创建%s步骤原始时间刻度文件夹: %s\n', stepType, originalTimelineFolder);
                end
            end
        end

        if config.saveResetTimeline
            resetTimelineFolder = fullfile(outputFolder, config.resetTimelineFolder);
            if ~exist(resetTimelineFolder, 'dir')
                mkdir(resetTimelineFolder);
                if config.enableVerboseOutput
                    fprintf('创建%s步骤重置时间刻度文件夹: %s\n', stepType, resetTimelineFolder);
                end
            end
        end
    end
    
    %% 显示分割信息
    if config.enableVerboseOutput
        fprintf('=== %s步骤分割信息 ===\n', upper(stepType));
        fprintf('原始时长: %.2f 秒 (%d 样本)\n', totalDuration, totalSamples);
        fprintf('片段长度: %.2f 秒 (%d 样本)\n', config.secondarySegmentLength, segmentLengthSamples);
        fprintf('重叠长度: %.2f 秒 (%d 样本)\n', overlapSamples/samplingRate, overlapSamples);
        fprintf('预计片段数: %d\n', numSegments);
        fprintf('==================\n');
    end
    
    %% 执行分割和保存
    savedCount = 0;
    
    for segIdx = 1:numSegments
        % 计算当前片段的起始和结束位置
        startIdx = (segIdx - 1) * stepSamples + 1;
        endIdx = min(startIdx + segmentLengthSamples - 1, totalSamples);
        currentSegmentLength = endIdx - startIdx + 1;

        if config.enableVerboseOutput
            fprintf('%s片段 %d: 起始=%d, 结束=%d, 长度=%d样本(%.2f秒)\n', ...
                stepType, segIdx, startIdx, endIdx, currentSegmentLength, currentSegmentLength/samplingRate);
        end

        if currentSegmentLength < minSegmentSamples
            if config.enableVerboseOutput
                fprintf('  -> %s片段 %d 长度不足，跳过保存\n', stepType, segIdx);
            end
            continue;
        end
        
        % 提取当前片段的数据
        segmentTT_ch2_original = tt_ch2(startIdx:endIdx, :);
        segmentTT_ch3_original = tt_ch3(startIdx:endIdx, :);

        % 生成片段文件名和变量名
        segmentSuffix = sprintf('_seg%03d', segIdx);
        var1Name = [cleanBaseName, segmentSuffix, '_tt1'];
        var2Name = [cleanBaseName, segmentSuffix, '_tt2'];

        % 保存数据到不同的时间刻度文件夹
        segmentSaved = false;

        try
            % 保存原始时间刻度版本
            if config.enableDualTimeScale && config.saveOriginalTimeline
                originalFileName = fullfile(originalTimelineFolder, [cleanBaseName, segmentSuffix, '_tt.mat']);

                eval([var1Name, ' = segmentTT_ch2_original;']);
                eval([var2Name, ' = segmentTT_ch3_original;']);
                save(originalFileName, var1Name, var2Name);

                if config.enableProgressDisplay
                    fprintf('已保存%s步骤原始时间刻度片段 %d: %s\n', ...
                        stepType, segIdx, [cleanBaseName, segmentSuffix, '_tt.mat']);
                end
                segmentSaved = true;
            end

            % 保存重置时间刻度版本
            if config.enableDualTimeScale && config.saveResetTimeline
                resetFileName = fullfile(resetTimelineFolder, [cleanBaseName, segmentSuffix, '_tt.mat']);

                % 创建重置时间刻度的时间表
                segmentTT_ch2_reset = createResetTimeTable(segmentTT_ch2_original, config.samplingRate);
                segmentTT_ch3_reset = createResetTimeTable(segmentTT_ch3_original, config.samplingRate);

                eval([var1Name, ' = segmentTT_ch2_reset;']);
                eval([var2Name, ' = segmentTT_ch3_reset;']);
                save(resetFileName, var1Name, var2Name);

                if config.enableProgressDisplay
                    fprintf('已保存%s步骤重置时间刻度片段 %d: %s\n', ...
                        stepType, segIdx, [cleanBaseName, segmentSuffix, '_tt.mat']);
                end
                segmentSaved = true;
            end

            % 如果没有启用双时间刻度，使用原有逻辑
            if ~config.enableDualTimeScale
                segmentFileName = fullfile(outputFolder, [cleanBaseName, segmentSuffix, '_tt.mat']);
                eval([var1Name, ' = segmentTT_ch2_original;']);
                eval([var2Name, ' = segmentTT_ch3_original;']);
                save(segmentFileName, var1Name, var2Name);
                segmentSaved = true;

                if config.enableProgressDisplay
                    fprintf('已保存%s步骤片段 %d: %s\n', ...
                        stepType, segIdx, [cleanBaseName, segmentSuffix, '_tt.mat']);
                end
            end

            if segmentSaved
                savedCount = savedCount + 1;
            end

        catch ME
            if config.enableErrorHandling
                warning('segmentAndSaveStepTimeTable:SaveFailed', ...
                    '%s步骤片段 %d 保存失败: %s', stepType, segIdx, ME.message);
            else
                rethrow(ME);
            end
        end
    end
    
    %% 输出总结信息
    if config.enableVerboseOutput
        fprintf('\n=== %s步骤分割完成 ===\n', upper(stepType));
        fprintf('成功保存片段数: %d/%d\n', savedCount, numSegments);
        if config.enableDualTimeScale
            if config.saveOriginalTimeline
                fprintf('原始时间刻度文件夹: %s\n', originalTimelineFolder);
            end
            if config.saveResetTimeline
                fprintf('重置时间刻度文件夹: %s\n', resetTimelineFolder);
            end
        else
            fprintf('输出文件夹: %s\n', outputFolder);
        end
        fprintf('===================\n\n');
    end
end

function resetTT = createResetTimeTable(originalTT, samplingRate)
%CREATERESETTIMETABLE 创建重置时间刻度的时间表
    data = originalTT.Variables;
    resetTT = timetable(data, 'SampleRate', samplingRate);
end
