function plot_waveform(t, data, position, rows, cols, index, title_text, varargin)
%PLOT_WAVEFORM 肠鸣音信号波形绘制函数
%   绘制时域信号波形图，专门用于肠鸣音音频信号的可视化分析。
%   该函数提供专业的科研级别图形格式，适用于医学信号处理研究。
%
%   语法:
%   plot_waveform(t, data, position, rows, cols, index, title_text)
%   plot_waveform(t, data, position, rows, cols, index, title_text, time_range)
%
%   输入参数:
%   t          - 时间向量 (duration数组，MATLAB时间格式)
%   data       - 信号数据 (数值向量，归一化幅值)
%   position   - 图形窗口位置 ([left, bottom, width, height])
%   rows       - 子图行数 (正整数)
%   cols       - 子图列数 (正整数)
%   index      - 当前子图索引 (正整数)
%   title_text - 图形标题 (字符串)
%   time_range - 可选，时间轴范围 ([t_min, t_max]，单位：秒)
%
%   输出参数:
%   无 - 直接生成图形显示
%
%   图形特征:
%   - 信号颜色: 深蓝色 RGB[56,82,151]
%   - Y轴范围: [-1, 1] (归一化幅值)
%   - X轴范围: 自动适应数据长度或用户指定范围
%   - Y轴刻度: [-1, -0.5, 0, 0.5, 1]
%   - 字体设置: Times New Roman, 加粗, 16pt刻度, 18pt标签
%
%   技术参数:
%   - 时间转换: duration格式转换为秒数显示
%   - 幅值范围: 假设信号已归一化到[-1,1]区间
%   - 显示时长: 动态适应数据长度或用户指定范围
%
%   应用场景:
%   - 肠鸣音信号时域特征观察
%   - 信号质量评估和异常检测
%   - 多通道信号对比分析
%   - 科研论文图形制作
%
%   示例:
%   % 基本用法
%   fs = 2570; % 采样率
%   t = seconds(0:1/fs:299); % 5分钟时间向量
%   signal = 0.5*sin(2*pi*100*t); % 100Hz测试信号
%   position = [200, 200, 1500, 900];
%   plot_waveform(t, signal, position, 1, 1, 1, '测试信号波形');
%
%   % 多子图用法
%   figure('Position', [100, 100, 1200, 800]);
%   plot_waveform(t, signal1, position, 2, 1, 1, '通道1');
%   plot_waveform(t, signal2, position, 2, 1, 2, '通道2');
%
%   注意事项:
%   - 时间向量t必须为duration格式
%   - 信号数据建议预先归一化到[-1,1]范围
%   - position参数影响整个figure窗口，不仅是子图
%   - 时间范围自动适应数据长度，可通过time_range参数自定义
%
%   参见: PLOT_SPECTROGRAM, PLOT_INTENSITY, SUBPLOT, DURATION
%
%   作者: [医学信号处理团队]
%   创建日期: [创建日期]
%   版本: 1.0
    % 解析可选的时间范围参数
    if nargin >= 8 && ~isempty(varargin{1})
        time_range = varargin{1};
    else
        % 默认使用数据的实际时间范围
        time_range = [0, seconds(t(end))];
    end

    figure('Position', position); % 设置图框位置和大小，[left, bottom, width, height]
    subplot(rows, cols, index);

    color1 = [56/255, 82/255, 151/255];
    % plot(t, data,'Color', color1);
    plot(seconds(t), data,'Color', color1);
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.XLim = time_range;
    % ax.YLim = [-1 1];
    % yticks([-1 -0.5 0 0.5 1]); % 自定义纵坐标刻度
    ax.YLim = [-0.5 0.5];
    yticks([-0.5 -0.25 0 0.25 0.5]); % 自定义纵坐标刻度
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end