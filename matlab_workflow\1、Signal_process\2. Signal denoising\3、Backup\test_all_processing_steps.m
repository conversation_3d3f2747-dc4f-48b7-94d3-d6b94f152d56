%TEST_ALL_PROCESSING_STEPS 测试所有处理步骤保存功能
%   该脚本用于测试修改后的信号处理流程，验证按步骤编号的文件夹结构和
%   原始数据保存功能。可以在不处理大量文件的情况下快速验证新功能的正确性。
%
%   功能测试:
%   1. 新的配置参数验证
%   2. 原始数据保存功能
%   3. 按步骤编号的文件夹结构创建
%   4. 所有步骤的分割功能测试
%   5. 新的文件命名规范验证
%
%   使用方法:
%   1. 确保有测试用的CSV文件
%   2. 运行此脚本进行功能验证
%   3. 检查生成的文件结构和内容
%
%   输出结构:
%   2、Processed data/
%   ├── 1_preprocessed_data/       # 预处理数据转换结果（去直流、去趋势、归一化）
%   │   ├── original_timeline/     # 原始时间刻度分割文件
%   │   └── reset_timeline/        # 重置时间刻度分割文件
%   ├── 2_bandpass_results/        # 带通滤波结果
%   │   ├── original_timeline/     # 原始时间刻度分割文件
%   │   └── reset_timeline/        # 重置时间刻度分割文件
%   ├── 3_spectral_results/        # 谱减法结果
%   │   ├── original_timeline/     # 原始时间刻度分割文件
%   │   └── reset_timeline/        # 重置时间刻度分割文件
%   └── 4_final_results/           # 最终完整处理结果
%       ├── original_timeline/     # 原始时间刻度分割文件
%       └── reset_timeline/        # 重置时间刻度分割文件

clear all;
clc;
close all;

%% 添加函数路径
currentDir = fileparts(mfilename('fullpath'));
functionDir = fullfile(currentDir, '0、function', '2、Spectral Subtraction');
if exist(functionDir, 'dir')
    addpath(functionDir);
    fprintf('✓ 成功添加函数路径: %s\n', functionDir);
else
    error('函数文件夹不存在: %s', functionDir);
end

%% 创建测试配置
config = createProcessingConfig();

% 启用所有处理步骤保存
config.saveAllProcessingSteps = true;
config.saveOriginalData = true;
config.enableStepSegmentation = true;

% 设置较短的分割长度用于测试
config.secondarySegmentLength = 30;  % 30秒片段，便于测试
config.minSegmentLength = 5;         % 最小5秒

% 启用详细输出
config.enableVerboseOutput = true;
config.enableProgressDisplay = true;

fprintf('=== 测试配置 ===\n');
if config.saveAllProcessingSteps
    fprintf('所有步骤保存: 启用\n');
else
    fprintf('所有步骤保存: 禁用\n');
end
if config.savePreprocessedData
    fprintf('预处理数据保存: 启用\n');
else
    fprintf('预处理数据保存: 禁用\n');
end
if config.enableStepSegmentation
    fprintf('步骤分割: 启用\n');
else
    fprintf('步骤分割: 禁用\n');
end
fprintf('文件夹结构:\n');
fprintf('  1. %s (预处理数据：去直流、去趋势、归一化)\n', config.preprocessedDataFolder);
fprintf('  2. %s (带通滤波)\n', config.bandpassResultsFolder);
fprintf('  3. %s (谱减法)\n', config.spectralResultsFolder);
fprintf('  4. %s (最终结果)\n', config.finalResultsFolder);
fprintf('分割长度: %.1f 秒\n', config.secondarySegmentLength);
fprintf('===============\n\n');

%% 选择测试文件
fprintf('请选择一个CSV文件进行测试...\n');
[fileName, pathName] = uigetfile('*.csv', '选择测试用的CSV文件');
if fileName == 0
    disp('用户取消了文件选择');
    return;
end

testFile = fullfile(pathName, fileName);
fprintf('选择的测试文件: %s\n\n', testFile);

%% 处理测试文件
try
    fprintf('=== 开始处理测试文件 ===\n');
    
    % 读取CSV文件
    data = readtable(testFile);
    data(1, :) = []; % 删除第一行
    
    % 验证CSV文件格式
    if size(data, 2) < max(config.requiredColumns)
        error('CSV文件列数不足');
    end
    
    % 提取第二列和第三列数据
    column2 = data{:, config.requiredColumns(1)};
    column3 = data{:, config.requiredColumns(2)};
    
    % 验证数据有效性
    if any(isnan(column2)) || any(isnan(column3))
        warning('数据包含NaN值，可能影响处理结果');
    end
    
    fprintf('数据长度: %d 样本 (%.2f 秒)\n', length(column2), length(column2)/config.samplingRate);
    
    %% 保存原始信号数据
    originalSignals = {column2, column3};
    
    %% 调用处理函数
    inputSignals = {column2, column3};
    [processedSignals, snrResults, intermediateResults] = processDualChannelSignals(inputSignals, config);
    
    %% 显示SNR结果
    fprintf('\n=== SNR结果 ===\n');
    fprintf('带通滤波后 column2 的 SNR = %.4f dB\n', snrResults.bandpassSNR(1));
    fprintf('带通滤波后 column3 的 SNR = %.4f dB\n', snrResults.bandpassSNR(2));
    if config.enableSpectralSubtraction
        fprintf('谱减滤波后 column2 的 SNR = %.4f dB\n', snrResults.spectralSubtractionSNR(1));
        fprintf('谱减滤波后 column3 的 SNR = %.4f dB\n', snrResults.spectralSubtractionSNR(2));
    end
    fprintf('===============\n\n');
    
    %% 创建输出文件夹
    outputFolder = fullfile(currentDir, 'test_output_all_steps');
    if ~exist(outputFolder, 'dir')
        mkdir(outputFolder);
        fprintf('创建测试输出文件夹: %s\n', outputFolder);
    end
    
    %% 保存所有处理步骤结果
    if config.saveAllProcessingSteps
        fprintf('=== 保存所有处理步骤结果 ===\n');
        saveAllProcessingSteps(originalSignals, intermediateResults, processedSignals, testFile, outputFolder, config);
    end
    
    %% 验证输出结构
    fprintf('\n=== 验证输出结构 ===\n');
    verifyAllStepsOutputStructure(outputFolder, config);
    
    %% 验证文件内容
    fprintf('\n=== 验证文件内容 ===\n');
    verifyFileContents(outputFolder, config, testFile);
    
    fprintf('\n=== 测试完成 ===\n');
    fprintf('测试输出文件夹: %s\n', outputFolder);
    fprintf('请检查生成的文件结构和内容\n');
    
catch ME
    fprintf('\n=== 测试失败 ===\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    rethrow(ME);
end

%% 清理路径
if exist('functionDir', 'var') && exist(functionDir, 'dir')
    rmpath(functionDir);
    fprintf('✓ 已清理函数路径\n');
end

function verifyAllStepsOutputStructure(outputFolder, config)
%VERIFYALLSTEPSOUTPUTSTRUCTURE 验证所有步骤的输出文件夹结构
    fprintf('检查输出文件夹结构...\n');
    
    % 检查主文件夹
    if exist(outputFolder, 'dir')
        fprintf('✓ 主输出文件夹存在: %s\n', outputFolder);
    else
        fprintf('✗ 主输出文件夹不存在\n');
        return;
    end
    
    % 定义步骤文件夹和名称
    stepFolders = {
        config.preprocessedDataFolder, '预处理数据';
        config.bandpassResultsFolder, '带通滤波结果';
        config.spectralResultsFolder, '谱减法结果';
        config.finalResultsFolder, '最终结果'
    };
    
    % 检查各步骤文件夹
    for i = 1:size(stepFolders, 1)
        stepFolder = fullfile(outputFolder, stepFolders{i, 1});
        stepName = stepFolders{i, 2};
        
        if exist(stepFolder, 'dir')
            fprintf('✓ %s文件夹存在: %s\n', stepName, stepFolders{i, 1});
            checkStepSubfolders(stepFolder, config, stepName);
        else
            fprintf('✗ %s文件夹不存在: %s\n', stepName, stepFolders{i, 1});
        end
    end
end

function checkStepSubfolders(stepFolder, config, stepName)
%CHECKSTEPSUBFOLDERS 检查步骤子文件夹结构
    if config.enableStepSegmentation && config.enableDualTimeScale
        originalFolder = fullfile(stepFolder, config.originalTimelineFolder);
        resetFolder = fullfile(stepFolder, config.resetTimelineFolder);
        
        if exist(originalFolder, 'dir')
            fprintf('  ✓ %s原始时间刻度子文件夹存在\n', stepName);
        else
            fprintf('  ✗ %s原始时间刻度子文件夹不存在\n', stepName);
        end
        
        if exist(resetFolder, 'dir')
            fprintf('  ✓ %s重置时间刻度子文件夹存在\n', stepName);
        else
            fprintf('  ✗ %s重置时间刻度子文件夹不存在\n', stepName);
        end
    end
end

function verifyFileContents(outputFolder, config, testFile)
%VERIFYFILECONTENTS 验证文件内容和命名规范
    [~, baseName, ~] = fileparts(testFile);
    cleanBaseName = regexprep(baseName, '[^a-zA-Z0-9_]', '_');
    if ~isempty(cleanBaseName) && ~isletter(cleanBaseName(1))
        cleanBaseName = ['file_', cleanBaseName];
    end
    
    % 定义步骤类型和对应的文件夹
    stepTypes = {'preprocessed', 'bandpass', 'spectral', 'final'};
    stepFolders = {
        config.preprocessedDataFolder;
        config.bandpassResultsFolder;
        config.spectralResultsFolder;
        config.finalResultsFolder
    };
    
    for i = 1:length(stepTypes)
        stepType = stepTypes{i};
        stepFolder = fullfile(outputFolder, stepFolders{i});
        
        if exist(stepFolder, 'dir')
            fprintf('检查%s步骤文件...\n', stepType);
            
            if config.enableStepSegmentation && config.enableDualTimeScale
                % 检查分割文件
                originalTimelineFolder = fullfile(stepFolder, config.originalTimelineFolder);
                resetTimelineFolder = fullfile(stepFolder, config.resetTimelineFolder);
                
                checkSegmentFiles(originalTimelineFolder, cleanBaseName, stepType, '原始时间刻度');
                checkSegmentFiles(resetTimelineFolder, cleanBaseName, stepType, '重置时间刻度');
            else
                % 检查完整文件
                expectedFileName = [cleanBaseName, '_', stepType, '_tt.mat'];
                fullFilePath = fullfile(stepFolder, expectedFileName);
                
                if exist(fullFilePath, 'file')
                    fprintf('  ✓ %s步骤完整文件存在: %s\n', stepType, expectedFileName);
                else
                    fprintf('  ✗ %s步骤完整文件不存在: %s\n', stepType, expectedFileName);
                end
            end
        end
    end
end

function checkSegmentFiles(folder, baseName, stepType, timelineType)
%CHECKSEGMENTFILES 检查分割文件
    if exist(folder, 'dir')
        matFiles = dir(fullfile(folder, '*.mat'));
        expectedPattern = [baseName, '_', stepType, '_seg'];
        
        matchingFiles = 0;
        for i = 1:length(matFiles)
            if contains(matFiles(i).name, expectedPattern)
                matchingFiles = matchingFiles + 1;
            end
        end
        
        if matchingFiles > 0
            fprintf('  ✓ %s步骤%s分割文件: 找到%d个文件\n', stepType, timelineType, matchingFiles);
        else
            fprintf('  ✗ %s步骤%s分割文件: 未找到匹配文件\n', stepType, timelineType);
        end
    else
        fprintf('  ✗ %s步骤%s文件夹不存在\n', stepType, timelineType);
    end
end
