%% 测试photograph.m交互式选择功能的脚本
% 该脚本用于验证photograph.m的交互式变量选择功能

% 清空工作空间
clear all;
close all;

% 添加函数路径
addpath('../function');

% 测试数据文件路径
test_file = '1、Raw data/data4_5min_seg005_tt_yes_15.mat';

% 检查测试文件是否存在
if ~exist(test_file, 'file')
    error('测试文件不存在: %s', test_file);
end

% 加载测试文件并检查变量
fprintf('正在加载测试文件: %s\n', test_file);
load(test_file);

% 显示加载的所有变量
loaded_vars = who;
fprintf('加载的变量:\n');
for i = 1:length(loaded_vars)
    var_name = loaded_vars{i};
    var_info = whos(var_name);
    fprintf('  %s: %s [%s]\n', var_name, var_info.class, num2str(var_info.size));
end

% 查找timetable变量
timetable_vars = {};
for i = 1:length(loaded_vars)
    var_name = loaded_vars{i};
    if exist(var_name, 'var') && istimetable(eval(var_name))
        timetable_vars{end+1} = var_name;
        tt_var = eval(var_name);
        fprintf('找到timetable变量: %s (大小: %d行, %d列)\n', ...
            var_name, height(tt_var), width(tt_var));
        
        % 显示timetable的基本信息
        if height(tt_var) > 0
            fprintf('  时间范围: %s 到 %s\n', ...
                string(tt_var.Time(1)), string(tt_var.Time(end)));
            fprintf('  变量名: %s\n', strjoin(tt_var.Properties.VariableNames, ', '));
        end
    end
end

if isempty(timetable_vars)
    error('未找到任何timetable变量');
else
    fprintf('\n总共找到 %d 个timetable变量\n', length(timetable_vars));
end

% 测试变量选择逻辑
fprintf('\n测试变量选择逻辑:\n');
if length(timetable_vars) == 1
    selected_var = timetable_vars{1};
    fprintf('只有一个timetable变量，选择: %s\n', selected_var);
else
    % 查找包含tt1的变量
    tt1_candidates = {};
    for i = 1:length(timetable_vars)
        if contains(timetable_vars{i}, 'tt1')
            tt1_candidates{end+1} = timetable_vars{i};
        end
    end
    
    if ~isempty(tt1_candidates)
        selected_var = tt1_candidates{1};
        fprintf('选择包含tt1的变量: %s\n', selected_var);
        if length(tt1_candidates) > 1
            fprintf('其他tt1候选变量: %s\n', strjoin(tt1_candidates(2:end), ', '));
        end
    else
        selected_var = timetable_vars{1};
        fprintf('未找到包含tt1的变量，选择第一个: %s\n', selected_var);
    end
end

% 验证选定变量的数据质量
selected_tt = eval(selected_var);
fprintf('\n验证选定变量的数据质量:\n');
fprintf('变量名: %s\n', selected_var);
fprintf('数据点数: %d\n', height(selected_tt));
fprintf('数据列数: %d\n', width(selected_tt));

if height(selected_tt) > 0
    time_duration = seconds(selected_tt.Time(end) - selected_tt.Time(1));
    fprintf('时间长度: %.2f 秒\n', time_duration);
    
    % 检查信号数据
    signal_data = selected_tt.Variables;
    fprintf('信号数据范围: [%.6f, %.6f]\n', min(signal_data), max(signal_data));
    fprintf('信号数据均值: %.6f\n', mean(signal_data));
    fprintf('信号数据标准差: %.6f\n', std(signal_data));
end

% 演示交互式选择界面
if length(timetable_vars) > 1
    fprintf('\n=== 演示交互式选择界面 ===\n');
    fprintf('当运行photograph.m时，您将看到如下选择界面:\n\n');

    fprintf('发现多个timetable变量:\n');
    for i = 1:length(timetable_vars)
        var_name = timetable_vars{i};
        var_data = eval(var_name);
        time_duration = seconds(var_data.Time(end) - var_data.Time(1));
        fprintf('  [%d] %s (数据点: %d, 时长: %.1f秒)\n', ...
            i, var_name, height(var_data), time_duration);
    end
    fprintf('\n请选择要处理的变量 (1-%d): \n', length(timetable_vars));
    fprintf('(您可以输入1选择tt1数据，或输入2选择tt2数据)\n');
end

fprintf('\n测试完成！photograph.m现在支持交互式变量选择功能。\n');
