%MIGRATE_FOLDER_STRUCTURE 迁移现有文件夹结构到新的编号结构
%   该脚本帮助用户将现有的文件夹结构迁移到新的按步骤编号的结构。
%   它会安全地重命名现有文件夹，并创建新的文件夹结构。
%
%   迁移内容:
%   旧结构 -> 新结构
%   bandpass_results -> 2_bandpass_results
%   spectral_results -> 3_spectral_results
%   original_timeline -> 4_final_results/original_timeline
%   reset_timeline -> 4_final_results/reset_timeline
%
%   新增内容:
%   1_original_data/ (新创建，用于存放原始数据转换结果)
%
%   使用方法:
%   1. 运行此脚本
%   2. 选择要迁移的"2、Processed data"文件夹
%   3. 脚本会自动进行迁移并创建备份
%
%   安全措施:
%   - 迁移前创建备份
%   - 检查文件夹是否存在
%   - 提供详细的迁移日志
%   - 支持回滚操作

clear all;
clc;
close all;

fprintf('=== 文件夹结构迁移工具 ===\n');
fprintf('该工具将帮助您将现有的文件夹结构迁移到新的编号结构\n');
fprintf('迁移内容:\n');
fprintf('  bandpass_results -> 2_bandpass_results\n');
fprintf('  spectral_results -> 3_spectral_results\n');
fprintf('  original_timeline -> 4_final_results/original_timeline\n');
fprintf('  reset_timeline -> 4_final_results/reset_timeline\n');
fprintf('新增:\n');
fprintf('  1_original_data/ (新创建)\n');
fprintf('========================\n\n');

%% 选择要迁移的文件夹
fprintf('请选择要迁移的"2、Processed data"文件夹...\n');
processedDataFolder = uigetdir('', '选择"2、Processed data"文件夹');
if processedDataFolder == 0
    disp('用户取消了文件夹选择');
    return;
end

fprintf('选择的文件夹: %s\n\n', processedDataFolder);

%% 检查现有结构
fprintf('=== 检查现有文件夹结构 ===\n');
oldStructure = checkExistingStructure(processedDataFolder);
displayStructureCheck(oldStructure);

%% 确认迁移
fprintf('\n是否继续进行迁移？(y/n): ');
userInput = input('', 's');
if ~strcmpi(userInput, 'y')
    fprintf('迁移已取消\n');
    return;
end

%% 创建备份
fprintf('\n=== 创建备份 ===\n');
backupFolder = createBackup(processedDataFolder);

%% 执行迁移
fprintf('\n=== 执行迁移 ===\n');
try
    migrationLog = performMigration(processedDataFolder, oldStructure);
    
    fprintf('\n=== 迁移完成 ===\n');
    fprintf('备份位置: %s\n', backupFolder);
    displayMigrationLog(migrationLog);
    
    fprintf('\n=== 验证新结构 ===\n');
    verifyNewStructure(processedDataFolder);
    
    fprintf('\n迁移成功完成！\n');
    fprintf('如果需要回滚，请运行: rollback_migration.m\n');
    
catch ME
    fprintf('\n=== 迁移失败 ===\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('正在尝试回滚...\n');
    
    try
        rollbackMigration(processedDataFolder, backupFolder);
        fprintf('回滚成功\n');
    catch rollbackME
        fprintf('回滚失败: %s\n', rollbackME.message);
        fprintf('请手动从备份文件夹恢复: %s\n', backupFolder);
    end
    
    rethrow(ME);
end

function structure = checkExistingStructure(folder)
%CHECKEXISTINGSTRUCTURE 检查现有文件夹结构
    structure = struct();
    structure.bandpassResults = exist(fullfile(folder, 'bandpass_results'), 'dir') == 7;
    structure.spectralResults = exist(fullfile(folder, 'spectral_results'), 'dir') == 7;
    structure.originalTimeline = exist(fullfile(folder, 'original_timeline'), 'dir') == 7;
    structure.resetTimeline = exist(fullfile(folder, 'reset_timeline'), 'dir') == 7;
    
    % 检查新结构是否已存在
    structure.newOriginalData = exist(fullfile(folder, '1_original_data'), 'dir') == 7;
    structure.newBandpassResults = exist(fullfile(folder, '2_bandpass_results'), 'dir') == 7;
    structure.newSpectralResults = exist(fullfile(folder, '3_spectral_results'), 'dir') == 7;
    structure.newFinalResults = exist(fullfile(folder, '4_final_results'), 'dir') == 7;
end

function displayStructureCheck(structure)
%DISPLAYSTRUCTURECHECK 显示结构检查结果
    fprintf('现有文件夹:\n');
    fprintf('  bandpass_results: %s\n', structure.bandpassResults ? '存在' : '不存在');
    fprintf('  spectral_results: %s\n', structure.spectralResults ? '存在' : '不存在');
    fprintf('  original_timeline: %s\n', structure.originalTimeline ? '存在' : '不存在');
    fprintf('  reset_timeline: %s\n', structure.resetTimeline ? '存在' : '不存在');
    
    fprintf('\n新结构文件夹:\n');
    fprintf('  1_original_data: %s\n', structure.newOriginalData ? '已存在' : '将创建');
    fprintf('  2_bandpass_results: %s\n', structure.newBandpassResults ? '已存在' : '将创建');
    fprintf('  3_spectral_results: %s\n', structure.newSpectralResults ? '已存在' : '将创建');
    fprintf('  4_final_results: %s\n', structure.newFinalResults ? '已存在' : '将创建');
end

function backupFolder = createBackup(processedDataFolder)
%CREATEBACKUP 创建备份
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    backupFolder = fullfile(fileparts(processedDataFolder), ['backup_processed_data_', timestamp]);
    
    fprintf('创建备份文件夹: %s\n', backupFolder);
    copyfile(processedDataFolder, backupFolder);
    fprintf('备份创建完成\n');
end

function migrationLog = performMigration(folder, structure)
%PERFORMMIGRATION 执行迁移
    migrationLog = {};
    
    % 1. 创建1_original_data文件夹
    if ~structure.newOriginalData
        originalDataFolder = fullfile(folder, '1_original_data');
        mkdir(originalDataFolder);
        mkdir(fullfile(originalDataFolder, 'original_timeline'));
        mkdir(fullfile(originalDataFolder, 'reset_timeline'));
        migrationLog{end+1} = '创建 1_original_data 文件夹及子文件夹';
    end
    
    % 2. 重命名bandpass_results -> 2_bandpass_results
    if structure.bandpassResults && ~structure.newBandpassResults
        oldPath = fullfile(folder, 'bandpass_results');
        newPath = fullfile(folder, '2_bandpass_results');
        movefile(oldPath, newPath);
        migrationLog{end+1} = '重命名 bandpass_results -> 2_bandpass_results';
    end
    
    % 3. 重命名spectral_results -> 3_spectral_results
    if structure.spectralResults && ~structure.newSpectralResults
        oldPath = fullfile(folder, 'spectral_results');
        newPath = fullfile(folder, '3_spectral_results');
        movefile(oldPath, newPath);
        migrationLog{end+1} = '重命名 spectral_results -> 3_spectral_results';
    end
    
    % 4. 创建4_final_results并移动original_timeline和reset_timeline
    if ~structure.newFinalResults
        finalResultsFolder = fullfile(folder, '4_final_results');
        mkdir(finalResultsFolder);
        migrationLog{end+1} = '创建 4_final_results 文件夹';
        
        if structure.originalTimeline
            oldPath = fullfile(folder, 'original_timeline');
            newPath = fullfile(finalResultsFolder, 'original_timeline');
            movefile(oldPath, newPath);
            migrationLog{end+1} = '移动 original_timeline -> 4_final_results/original_timeline';
        else
            mkdir(fullfile(finalResultsFolder, 'original_timeline'));
            migrationLog{end+1} = '创建 4_final_results/original_timeline';
        end
        
        if structure.resetTimeline
            oldPath = fullfile(folder, 'reset_timeline');
            newPath = fullfile(finalResultsFolder, 'reset_timeline');
            movefile(oldPath, newPath);
            migrationLog{end+1} = '移动 reset_timeline -> 4_final_results/reset_timeline';
        else
            mkdir(fullfile(finalResultsFolder, 'reset_timeline'));
            migrationLog{end+1} = '创建 4_final_results/reset_timeline';
        end
    end
end

function displayMigrationLog(migrationLog)
%DISPLAYMIGRATIONLOG 显示迁移日志
    fprintf('迁移操作日志:\n');
    for i = 1:length(migrationLog)
        fprintf('  %d. %s\n', i, migrationLog{i});
    end
end

function verifyNewStructure(folder)
%VERIFYNEWSTRUCTURE 验证新结构
    expectedFolders = {
        '1_original_data';
        '2_bandpass_results';
        '3_spectral_results';
        '4_final_results'
    };
    
    fprintf('验证新文件夹结构:\n');
    for i = 1:length(expectedFolders)
        folderPath = fullfile(folder, expectedFolders{i});
        if exist(folderPath, 'dir')
            fprintf('  ✓ %s\n', expectedFolders{i});
            
            % 检查子文件夹
            originalTimelinePath = fullfile(folderPath, 'original_timeline');
            resetTimelinePath = fullfile(folderPath, 'reset_timeline');
            
            if exist(originalTimelinePath, 'dir')
                fprintf('    ✓ original_timeline\n');
            else
                fprintf('    ✗ original_timeline (缺失)\n');
            end
            
            if exist(resetTimelinePath, 'dir')
                fprintf('    ✓ reset_timeline\n');
            else
                fprintf('    ✗ reset_timeline (缺失)\n');
            end
        else
            fprintf('  ✗ %s (缺失)\n', expectedFolders{i});
        end
    end
end

function rollbackMigration(folder, backupFolder)
%ROLLBACKMIGRATION 回滚迁移
    fprintf('正在回滚迁移...\n');
    
    % 删除当前文件夹内容
    rmdir(folder, 's');
    
    % 从备份恢复
    copyfile(backupFolder, folder);
    
    fprintf('回滚完成\n');
end
