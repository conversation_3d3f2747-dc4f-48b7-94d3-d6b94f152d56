# photograph.m 交互式选择与动态时间范围功能修复说明

## 问题描述

原始代码在第94行出现错误："在数据文件中找不到tt1"。这是因为：

1. **硬编码变量名问题**：原代码硬编码检查名为 `tt1` 的变量
2. **实际数据结构**：数据文件 `data4_5min_seg005_tt_yes_15.mat` 包含的变量名为：
   - `data4_5min_seg005_tt1`
   - `data4_5min_seg005_tt2`
3. **变量名不匹配**：代码期望的 `tt1` 与实际的 `data4_5min_seg005_tt1` 不匹配
4. **用户需求**：用户希望能够在命令行中选择处理tt1还是tt2数据
5. **固定时间范围问题**：绘图函数硬编码了0-300秒的时间范围，无法适应不同长度的数据

## 修复方案

### 1. 动态变量识别与交互式选择

### 2. 动态时间范围适应

**原问题**：
三个绘图函数都硬编码了0-300秒的时间范围：
- `plot_waveform.m` - 第75行：`ax.XLim = [0 300];`
- `plot_spectrogram.m` - 第102行：`ax.XLim = [0 300];`
- `plot_intensity.m` - 第123行：`ax.XLim = [0 300];`

**修复方案**：
1. **修改绘图函数签名**：为三个函数添加可选的 `time_range` 参数
2. **动态时间范围计算**：在 `photograph.m` 中根据实际数据长度计算时间范围
3. **智能刻度生成**：根据时间长度动态生成合适的X轴刻度
4. **向后兼容**：保持原有函数调用方式的兼容性

**绘图函数修改示例**（以plot_waveform为例）：
```matlab
% 新的函数签名
function plot_waveform(t, data, position, rows, cols, index, title_text, varargin)

% 时间范围参数解析
if nargin >= 8 && ~isempty(varargin{1})
    time_range = varargin{1};
else
    % 默认使用数据的实际时间范围
    time_range = [0, seconds(t(end))];
end

% 应用动态时间范围
ax.XLim = time_range;
```

**智能刻度生成逻辑**：
```matlab
% 根据时间长度动态生成刻度
time_span = time_range(2) - time_range(1);
if time_span <= 60
    tick_interval = 10;      % 小于1分钟，每10秒一个刻度
elseif time_span <= 300
    tick_interval = 50;      % 1-5分钟，每50秒一个刻度
else
    tick_interval = 60;      % 超过5分钟，每60秒一个刻度
end
tick_values = time_range(1):tick_interval:time_range(2);
xticks(tick_values);
```

**photograph.m中的时间范围计算**：
```matlab
% 计算实际数据的时间范围
time_start = 0; % 时间已归零，起始时间为0
time_end = seconds(time(end)); % 结束时间（秒）
time_range = [time_start, time_end];

% 显示数据信息
fprintf('数据时间范围: %.1f - %.1f 秒 (总时长: %.1f 秒)\n', ...
    time_start, time_end, time_end - time_start);

% 将时间范围传递给绘图函数
plot_waveform(time, signal, position, 2, 1, 1, 'Raw data of Mic (Body)', time_range);
plot_spectrogram(signal, fs, position, 2, 1, 2, 'Spectrogram of Mic (Body)', time_range);
plot_intensity(time, signal, fs, position, 2, 1, 1, 'Intensity of Mic (Body)', time_range);
```

**原代码（第92-104行）：**
```matlab
% 检查加载的数据中是否存在tt1
if ~exist('tt1', 'var')
    error('在数据文件中找不到tt1');
end

% 确保tt1是timetable格式
if ~istimetable(tt1)
    error('tt1不是timetable格式');
end

% 从timetable中提取时间和信号数据
time = tt1.Time - tt1.Time(1); % 将时间归零
signal = tt1.Variables; % 假设timetable只有一列数据
```

**修复后代码（交互式选择版本）：**
```matlab
% 动态检查加载的数据中的timetable变量
loaded_vars = who; % 获取所有加载的变量名
timetable_vars = {};
timetable_var_name = '';

% 查找所有timetable类型的变量
for i = 1:length(loaded_vars)
    var_name = loaded_vars{i};
    if exist(var_name, 'var') && istimetable(eval(var_name))
        timetable_vars{end+1} = var_name;
    end
end

% 检查是否找到timetable变量
if isempty(timetable_vars)
    error('在数据文件中找不到任何timetable格式的变量');
elseif length(timetable_vars) == 1
    % 只有一个timetable变量，直接使用
    timetable_var_name = timetable_vars{1};
    fprintf('找到timetable变量: %s\n', timetable_var_name);
else
    % 多个timetable变量，让用户选择
    fprintf('\n发现多个timetable变量:\n');
    for i = 1:length(timetable_vars)
        var_name = timetable_vars{i};
        var_data = eval(var_name);
        time_duration = seconds(var_data.Time(end) - var_data.Time(1));
        fprintf('  [%d] %s (数据点: %d, 时长: %.1f秒)\n', ...
            i, var_name, height(var_data), time_duration);
    end

    % 用户选择
    while true
        choice = input(sprintf('\n请选择要处理的变量 (1-%d): ', length(timetable_vars)));

        % 检查输入是否有效
        if isnumeric(choice) && isscalar(choice) && ...
           choice >= 1 && choice <= length(timetable_vars) && ...
           choice == round(choice)
            timetable_var_name = timetable_vars{choice};
            fprintf('您选择了: %s\n', timetable_var_name);
            break;
        else
            fprintf('无效选择，请输入1到%d之间的整数。\n', length(timetable_vars));
        end
    end
end

% 获取选定的timetable变量
selected_tt = eval(timetable_var_name);

% 从timetable中提取时间和信号数据
time = selected_tt.Time - selected_tt.Time(1); % 将时间归零
signal = selected_tt.Variables; % 假设timetable只有一列数据
```

### 3. 交互式变量选择逻辑

修复后的代码实现了以下交互式选择逻辑：

1. **扫描所有变量**：使用 `who` 函数获取所有加载的变量名
2. **识别timetable变量**：检查每个变量是否为timetable类型
3. **智能选择策略**：
   - 如果只有一个timetable变量，直接使用
   - 如果有多个timetable变量，显示选择菜单让用户选择
4. **交互式界面**：
   - 显示所有可用的timetable变量及其基本信息（数据点数、时长）
   - 提供编号选择界面
   - 输入验证和错误处理
   - 重新提示直到用户输入有效选择
5. **用户反馈**：通过 `fprintf` 告知用户选择了哪个变量

### 3. 文档更新

同时更新了代码注释，反映新的功能：

- **数据要求**：从"数据变量: tt1 (timetable格式)"改为"数据变量: 任何timetable格式的变量（优先选择包含'tt1'的变量）"
- **使用示例**：增加了"程序自动识别并选择合适的timetable变量"的说明
- **注意事项**：更新为更灵活的变量要求说明
- **错误处理**：增加了"多变量情况下的智能选择逻辑"

## 修复效果

### 处理能力提升

1. **兼容性增强**：能够处理各种命名格式的timetable变量
2. **自动识别**：无需手动修改变量名，程序自动识别合适的变量
3. **多变量支持**：能够处理包含多个timetable变量的数据文件
4. **用户友好**：提供清晰的变量选择反馈信息

### 具体应用场景

对于数据文件 `data4_5min_seg005_tt_yes_15.mat`：
- 包含变量：`data4_5min_seg005_tt1` 和 `data4_5min_seg005_tt2`
- 程序会显示交互式选择界面：
  ```
  发现多个timetable变量:
    [1] data4_5min_seg005_tt1 (数据点: 771000, 时长: 300.0秒)
    [2] data4_5min_seg005_tt2 (数据点: 771000, 时长: 300.0秒)

  请选择要处理的变量 (1-2):
  ```
- 用户可以输入 `1` 选择tt1数据，或输入 `2` 选择tt2数据
- 程序确认选择："您选择了: data4_5min_seg005_tt1"

## 测试验证

创建了多个测试脚本用于验证修复效果：

### 1. `test_photograph_fix.m` - 交互式选择功能测试
- 加载测试数据文件
- 显示所有变量信息
- 验证变量选择逻辑
- 检查数据质量

### 2. `test_dynamic_time_range.m` - 动态时间范围功能测试
- 测试不同长度数据的时间范围计算
- 验证智能刻度生成逻辑
- 测试三个绘图函数的时间范围参数
- 验证向后兼容性

## 总结

此次修复解决了原代码的硬编码变量名和固定时间范围问题，并增加了交互式选择功能，使程序具备了更强的适应性和用户友好性。修复后的代码能够：

1. **自动适应**不同的变量命名格式
2. **交互式选择**让用户自主选择要处理的timetable变量
3. **动态时间范围**自动适应任意长度的数据，不再局限于5分钟
4. **智能刻度生成**根据数据长度动态生成合适的X轴刻度
5. **输入验证**确保用户输入的有效性，提供错误处理和重新提示
6. **信息展示**显示每个变量的详细信息（数据点数、时长）帮助用户做出选择
7. **用户反馈**提供清晰的选择确认信息和时间范围信息
8. **向后兼容**保持原有函数调用方式的兼容性

### 新增功能特点

#### 交互式选择功能
- **用户自主权**：用户可以根据需要选择处理tt1还是tt2数据
- **信息透明**：显示每个变量的基本统计信息
- **错误容错**：输入验证和重新提示机制
- **操作简便**：简单的数字选择界面

#### 动态时间范围功能
- **自动适应**：支持任意长度的音频数据（不限于5分钟）
- **智能刻度**：根据数据长度自动生成合适的时间刻度
- **精确显示**：X轴范围完全匹配数据的实际时间长度
- **一致性**：三个绘图函数使用统一的时间范围设置

#### 技术改进
- **参数化设计**：绘图函数支持可选的时间范围参数
- **向后兼容**：保持原有函数调用方式不变
- **代码复用**：统一的刻度生成逻辑
- **性能优化**：避免不必要的数据截取和处理

这样的修复不仅解决了技术问题，还大大提升了用户体验和程序的适用性，让用户能够灵活地分析不同长度的肠鸣音数据，满足了多样化的研究需求。
