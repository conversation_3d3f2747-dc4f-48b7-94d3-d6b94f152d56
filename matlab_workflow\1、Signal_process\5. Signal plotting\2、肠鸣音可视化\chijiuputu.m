% 计算持久频谱

% Generated by MATLAB(R) 24.1 and Signal Processing Toolbox 24.1.
% Generated on: 26-Aug-2025 17:10:54

% 参数
timeLimits = seconds([0 59.99961]); % 秒
frequencyLimits = [0 1285]; % Hz
overlapPercent = 50;

%%
% 对感兴趣的信号时间区域进行索引
data3_5min_seg001_tt1_data_ROI = data3_5min_seg001_tt1(:,'data');
data3_5min_seg001_tt1_data_ROI = data3_5min_seg001_tt1_data_ROI(timerange(timeLimits(1),timeLimits(2),'closed'),1);

% 计算频谱估计值
% 不带输出参数运行该函数调用以绘制结果
pspectrum(data3_5min_seg001_tt1_data_ROI, ...
    'persistence', ...
    'FrequencyLimits',frequencyLimits, ...
    'OverlapPercent',overlapPercent);
