function plot_bowel_sound_frequency_with_duration(signal, fs, peak_times)
    % 肠鸣音频率计算和标注函数，并提取每分钟内肠鸣音的时间范围
    % 输入：
    %   signal - 输入信号
    %   fs - 采样频率
    %   peak_times - 每个峰值对应的时间刻度（由 peak_search 输出的）

    % 定义参数
    time_threshold = 1.2;            % 肠鸣音事件的时间阈值，单位为秒
    amplitude_threshold = 0.0001;    % 信号幅度的阈值，用于确定肠鸣音事件的边界
    consecutive_points = 5;          % 连续点的数量，用于计算平均幅度

    % 计算整个信号的总时长
    total_duration = peak_times(end);  % 整个信号的时长（秒）

    % 按分钟划分时长
    num_minutes = ceil(total_duration / 60);  % 计算总共有多少分钟

    % 初始化每分钟肠鸣音次数的数组
    bowel_sounds_per_minute = zeros(1, num_minutes);
    event_times_per_minute = cell(1, num_minutes);  % 用于存储每分钟肠鸣音事件的时间段

    % 创建输出目录
    output_dir = fullfile('.', 'output');
    if ~exist(output_dir, 'dir')
        mkdir(output_dir);
    end

    % 遍历每一分钟，计算对应的肠鸣音事件
    for minute_idx = 1:num_minutes
        % 定义该分钟的时间范围
        minute_start = (minute_idx - 1) * 60;  % 该分钟的开始时间
        minute_end = minute_idx * 60;          % 该分钟的结束时间

        % 获取在该分钟内的峰值时间
        minute_peaks = peak_times(peak_times >= minute_start & peak_times < minute_end);

        if ~isempty(minute_peaks)
            % **第一步：根据时间阈值确定肠鸣音个数**
            % 初始化事件列表
            events = [];
            event_start_idx = 1;

            for peak_idx = 2:length(minute_peaks)
                time_diff = minute_peaks(peak_idx) - minute_peaks(peak_idx - 1);
                if time_diff > time_threshold
                    % 记录一个事件（峰值索引范围）
                    events = [events; event_start_idx, peak_idx - 1];
                    event_start_idx = peak_idx;
                end
            end
            % 添加最后一个事件
            events = [events; event_start_idx, length(minute_peaks)];

            % 记录该分钟的肠鸣音事件数量
            num_events = size(events, 1);
            bowel_sounds_per_minute(minute_idx) = num_events;

            % **第二步：对每个事件，检测起始时间和结束时间**
            event_times = [];
            for event_idx = 1:num_events
                % 获取事件中所有峰值的索引范围
                peak_indices = events(event_idx, 1):events(event_idx, 2);
                % 获取事件中的峰值时间
                event_peak_times = minute_peaks(peak_indices);

                % 初始化事件的起始和结束时间
                event_start_time = inf;
                event_end_time = -inf;

                % 对事件中的每个峰值进行左右扩展
                for pt = event_peak_times'
                    % 将峰值时间转换为采样点索引
                    peak_sample_idx = round(pt * fs);

                    % 向左扩展
                    left_idx = peak_sample_idx;
                    while left_idx > 1
                        if left_idx - (consecutive_points - 1) < 1
                            break;
                        end
                        avg_amplitude = mean(abs(signal(left_idx - (consecutive_points - 1):left_idx)));
                        if avg_amplitude < amplitude_threshold
                            break;
                        else
                            left_idx = left_idx - 1;
                        end
                    end
                    % 确保事件开始索引有效
                    left_idx = max(left_idx - (consecutive_points - 1), 1);

                    % 向右扩展
                    right_idx = peak_sample_idx;
                    while right_idx < length(signal)
                        if right_idx + (consecutive_points - 1) > length(signal)
                            break;
                        end
                        avg_amplitude = mean(abs(signal(right_idx:right_idx + (consecutive_points - 1))));
                        if avg_amplitude < amplitude_threshold
                            break;
                        else
                            right_idx = right_idx + 1;
                        end
                    end
                    % 确保事件结束索引有效
                    right_idx = min(right_idx + (consecutive_points - 1), length(signal));

                    % 将索引转换为时间
                    current_start_time = (left_idx - 1) / fs;
                    current_end_time = (right_idx - 1) / fs;

                    % 更新事件的起始和结束时间
                    if current_start_time < event_start_time
                        event_start_time = current_start_time;
                    end
                    if current_end_time > event_end_time
                        event_end_time = current_end_time;
                    end
                end

                % 将事件时间段添加到该分钟的事件列表中
                event_times = [event_times; event_start_time, event_end_time];

                % **提取对应的时间序列和波形数据，并保存到CSV文件**
                % 计算事件的采样点索引范围
                event_start_idx = max(floor(event_start_time * fs) + 1, 1);
                event_end_idx = min(ceil(event_end_time * fs), length(signal));

                % 提取时间序列和信号数据
                event_time_series = (event_start_idx:event_end_idx)' / fs;
                event_signal = signal(event_start_idx:event_end_idx);

                % 创建保存数据的表格
                event_table = table(event_time_series, event_signal, 'VariableNames', {'Time', 'Signal'});

                % 构建文件名
                duration = event_end_time - event_start_time;  % 计算持续时间
                filename = sprintf('第%d分钟第%d次持续时间%.3f秒.csv', minute_idx, event_idx, duration);

                % 保存数据到 output 文件夹
                csv_filepath = fullfile(output_dir, filename);
                writetable(event_table, csv_filepath);
            end

            event_times_per_minute{minute_idx} = event_times;

        else
            bowel_sounds_per_minute(minute_idx) = 0;
        end
    end

    % 控制方框的高度，设为信号幅度的固定比例
    box_height = 0.4;  % 方框高度固定为 0.4，以适应 [-1, 1] 范围的信号

    % 绘制原始波形
    t = (0:length(signal)-1) / fs;  % 时间轴
    figure('Position', [300, 300, 1500, 900]);
    plot_waveform(t, signal, 2, 1, 1, 'Duration of Bowel Sound');
    hold on;

    % 用虚线方框标注肠鸣音事件的时间段
    for minute_idx = 1:num_minutes
        % 获取该分钟的肠鸣音事件时间段
        event_times = event_times_per_minute{minute_idx};

        % 如果该分钟有事件，绘制时间段
        if ~isempty(event_times)
            for event_idx = 1:size(event_times, 1)
                % 获取事件的开始时间和结束时间
                event_start = event_times(event_idx, 1);
                event_end = event_times(event_idx, 2);

                % 用红色虚线框圈出事件时间段
                rectangle('Position', [event_start, -box_height/2, event_end - event_start, box_height], ...
                          'EdgeColor', 'r', 'LineStyle', '--');
            end
        end

        % 在每分钟的中间位置标注肠鸣音的次数
        minute_center = (minute_idx - 0.5) * 60;  % 每分钟的中间位置
        text(minute_center, 0.8, ['BS/min: ', num2str(bowel_sounds_per_minute(minute_idx))], ...
             'HorizontalAlignment', 'center', 'Color', 'b', 'FontSize', 10, 'FontWeight', 'bold');
    end

    hold off;

    % 显示每分钟的肠鸣音次数及时间范围
    for i = 1:num_minutes
        disp(['第', num2str(i), '分钟的肠鸣音次数: ', num2str(bowel_sounds_per_minute(i)), ' 次']);
        if ~isempty(event_times_per_minute{i})
            disp('肠鸣音事件时间段:');
            for j = 1:size(event_times_per_minute{i}, 1)
                event_start = event_times_per_minute{i}(j, 1);
                event_end = event_times_per_minute{i}(j, 2);
                duration = event_end - event_start;  % 计算持续时间
                disp(['  第', num2str(j), '次: ', num2str(event_start), '秒 到 ', num2str(event_end), '秒，持续时间: ', num2str(duration), '秒']);
            end
        end
    end
end

















% function plot_bowel_sound_frequency_with_duration(signal, fs, peak_times)
%     % 肠鸣音频率计算和标注函数，并提取每分钟内肠鸣音的时间范围
%     % 输入：
%     %   signal - 输入信号
%     %   fs - 采样频率
%     %   peak_times - 每个峰值对应的时间刻度（由 peak_search 输出的）
% 
%     % 定义参数
%     time_threshold = 1.2;              % 肠鸣音事件的时间阈值，单位为秒
%     amplitude_threshold = 0.0001;    % 信号幅度的阈值，用于确定肠鸣音事件的边界
%     consecutive_points = 5;          % 连续点的数量，用于计算平均幅度
% 
%     % 计算整个信号的总时长
%     total_duration = peak_times(end);  % 整个信号的时长（秒）
% 
%     % 按分钟划分时长
%     num_minutes = ceil(total_duration / 60);  % 计算总共有多少分钟
% 
%     % 初始化每分钟肠鸣音次数的数组
%     bowel_sounds_per_minute = zeros(1, num_minutes);
%     event_times_per_minute = cell(1, num_minutes);  % 用于存储每分钟肠鸣音事件的时间段
% 
%     % 遍历每一分钟，计算对应的肠鸣音事件
%     for minute_idx = 1:num_minutes
%         % 定义该分钟的时间范围
%         minute_start = (minute_idx - 1) * 60;  % 该分钟的开始时间
%         minute_end = minute_idx * 60;          % 该分钟的结束时间
% 
%         % 获取在该分钟内的峰值时间
%         minute_peaks = peak_times(peak_times >= minute_start & peak_times < minute_end);
% 
%         if ~isempty(minute_peaks)
%             % **第一步：根据时间阈值确定肠鸣音个数**
%             % 初始化事件列表
%             events = [];
%             event_start_idx = 1;
% 
%             for peak_idx = 2:length(minute_peaks)
%                 time_diff = minute_peaks(peak_idx) - minute_peaks(peak_idx - 1);
%                 if time_diff > time_threshold
%                     % 记录一个事件（峰值索引范围）
%                     events = [events; event_start_idx, peak_idx - 1];
%                     event_start_idx = peak_idx;
%                 end
%             end
%             % 添加最后一个事件
%             events = [events; event_start_idx, length(minute_peaks)];
% 
%             % 记录该分钟的肠鸣音事件数量
%             num_events = size(events, 1);
%             bowel_sounds_per_minute(minute_idx) = num_events;
% 
%             % **第二步：对每个事件，检测起始时间和结束时间**
%             event_times = [];
%             for event_idx = 1:num_events
%                 % 获取事件中所有峰值的索引范围
%                 peak_indices = events(event_idx, 1):events(event_idx, 2);
%                 % 获取事件中的峰值时间
%                 event_peak_times = minute_peaks(peak_indices);
% 
%                 % 初始化事件的起始和结束时间
%                 event_start_time = inf;
%                 event_end_time = -inf;
% 
%                 % 对事件中的每个峰值进行左右扩展
%                 for pt = event_peak_times'
%                     % 将峰值时间转换为采样点索引
%                     peak_sample_idx = round(pt * fs);
% 
%                     % 向左扩展
%                     left_idx = peak_sample_idx;
%                     while left_idx > 1
%                         if left_idx - (consecutive_points - 1) < 1
%                             break;
%                         end
%                         avg_amplitude = mean(abs(signal(left_idx - (consecutive_points - 1):left_idx)));
%                         if avg_amplitude < amplitude_threshold
%                             break;
%                         else
%                             left_idx = left_idx - 1;
%                         end
%                     end
%                     % 确保事件开始索引有效
%                     left_idx = max(left_idx - (consecutive_points - 1), 1);
% 
%                     % 向右扩展
%                     right_idx = peak_sample_idx;
%                     while right_idx < length(signal)
%                         if right_idx + (consecutive_points - 1) > length(signal)
%                             break;
%                         end
%                         avg_amplitude = mean(abs(signal(right_idx:right_idx + (consecutive_points - 1))));
%                         if avg_amplitude < amplitude_threshold
%                             break;
%                         else
%                             right_idx = right_idx + 1;
%                         end
%                     end
%                     % 确保事件结束索引有效
%                     right_idx = min(right_idx + (consecutive_points - 1), length(signal));
% 
%                     % 将索引转换为时间
%                     current_start_time = (left_idx - 1) / fs;
%                     current_end_time = (right_idx - 1) / fs;
% 
%                     % 更新事件的起始和结束时间
%                     if current_start_time < event_start_time
%                         event_start_time = current_start_time;
%                     end
%                     if current_end_time > event_end_time
%                         event_end_time = current_end_time;
%                     end
%                 end
% 
%                 % 将事件时间段添加到该分钟的事件列表中
%                 event_times = [event_times; event_start_time, event_end_time];
%             end
% 
%             event_times_per_minute{minute_idx} = event_times;
% 
%         else
%             bowel_sounds_per_minute(minute_idx) = 0;
%         end
%     end
% 
%     % 控制方框的高度，设为信号幅度的固定比例
%     box_height = 0.4;  % 方框高度固定为 0.4，以适应 [-1, 1] 范围的信号
% 
%     % 调用 plot_waveform 绘制原始波形
%     t = (0:length(signal)-1) / fs;  % 时间轴
%     figure('Position', [300, 300, 1500, 900]);
%     plot_waveform(t, signal, 2, 1, 1, 'Duartion of Bowel Sound');
%     hold on;
% 
% 
% 
%     % 用虚线方框标注肠鸣音事件的时间段
%     for minute_idx = 1:num_minutes
%         % 获取该分钟的肠鸣音事件时间段
%         event_times = event_times_per_minute{minute_idx};
% 
%         % 如果该分钟有事件，绘制时间段
%         if ~isempty(event_times)
%             for event_idx = 1:size(event_times, 1)
%                 % 获取事件的开始时间和结束时间
%                 event_start = event_times(event_idx, 1);
%                 event_end = event_times(event_idx, 2);
% 
%                 % 用红色虚线框圈出事件时间段
%                 rectangle('Position', [event_start, -box_height/2, event_end - event_start, box_height], ...
%                           'EdgeColor', 'r', 'LineStyle', '--');
%             end
%         end
% 
%         % 在每分钟的中间位置标注肠鸣音的次数
%         minute_center = (minute_idx - 0.5) * 60;  % 每分钟的中间位置
%         text(minute_center, 0.8, ['BS/min: ', num2str(bowel_sounds_per_minute(minute_idx))], ...
%              'HorizontalAlignment', 'center', 'Color', 'b', 'FontSize', 10, 'FontWeight', 'bold');
%     end
% 
%     hold off;
% 
%     % 显示每分钟的肠鸣音次数及时间范围
%     for i = 1:num_minutes
%         disp(['第', num2str(i), '分钟的肠鸣音次数: ', num2str(bowel_sounds_per_minute(i)), ' 次']);
%         if ~isempty(event_times_per_minute{i})
%             disp('肠鸣音事件时间段:');
%             for j = 1:size(event_times_per_minute{i}, 1)
%                 event_start = event_times_per_minute{i}(j, 1);
%                 event_end = event_times_per_minute{i}(j, 2);
%                 duration = event_end - event_start;  % 计算持续时间
%                 disp(['  第', num2str(j), '次: ', num2str(event_start), '秒 到 ', num2str(event_end), '秒，持续时间: ', num2str(duration), '秒']);
%             end
%         end
%     end
% end











