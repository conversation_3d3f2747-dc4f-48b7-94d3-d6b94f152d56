function saveAllProcessingSteps(preprocessedSignals, intermediateResults, processedSignals, originalFileName, outputFolder, config)
%SAVEALLPROCESSINGSTEPS 保存所有处理步骤的结果并进行分割
%   按照处理顺序保存预处理数据、带通滤波、谱减法和最终结果的所有步骤。
%   该函数实现了完整的分步保存策略，支持双时间刻度保存和统一的文件组织结构。
%
%   语法:
%   saveAllProcessingSteps(preprocessedSignals, intermediateResults, processedSignals, originalFileName, outputFolder, config)
%
%   输入参数:
%   preprocessedSignals - 预处理信号数据 (元胞数组) {column2, column3}
%                        已经过去直流、去趋势、归一化处理
%   intermediateResults - 中间结果结构体，包含:
%                        .bandpassFiltered - 带通滤波结果 (元胞数组)
%                        .spectralSubtracted - 谱减法结果 (元胞数组)
%   processedSignals   - 最终处理结果 (元胞数组) {column2, column3}
%   originalFileName   - 原始文件名（用于生成输出文件名）
%   outputFolder       - 主输出文件夹路径
%   config            - 处理配置参数结构体
%
%   输出结果:
%   在指定文件夹的子文件夹中生成按步骤编号的结果文件：
%   - 1_preprocessed_data/: 预处理数据转换结果（去直流、去趋势、归一化）
%   - 2_bandpass_results/: 带通滤波结果
%   - 3_spectral_results/: 谱减法结果
%   - 4_final_results/: 最终完整处理结果
%   每个子文件夹内根据配置进一步分为：
%   - original_timeline/: 原始时间刻度分割文件
%   - reset_timeline/: 重置时间刻度分割文件
%
%   文件命名格式:
%   - 预处理数据：[文件名]_preprocessed_seg[序号]_tt.mat
%   - 带通滤波：[文件名]_bandpass_seg[序号]_tt.mat
%   - 谱减法：[文件名]_spectral_seg[序号]_tt.mat
%   - 最终结果：[文件名]_final_seg[序号]_tt.mat
%
%   配置参数:
%   config.saveAllProcessingSteps    - 是否保存所有处理步骤
%   config.savePreprocessedData      - 是否保存预处理数据
%   config.preprocessedDataFolder    - 预处理数据文件夹名
%   config.bandpassResultsFolder     - 带通滤波结果文件夹名
%   config.spectralResultsFolder     - 谱减法结果文件夹名
%   config.finalResultsFolder        - 最终结果文件夹名
%   config.enableStepSegmentation    - 是否对各步骤结果进行分割
%   config.samplingRate              - 采样率
%   其他分割相关配置参数...
%
%   处理流程:
%   1. 验证输入参数和各步骤数据
%   2. 创建按步骤编号的文件夹结构
%   3. 按顺序保存各步骤的处理结果
%   4. 如果启用分割，对各步骤结果进行分割处理
%   5. 生成处理报告和统计信息
%
%   示例:
%   % 基本用法
%   config = createProcessingConfig();
%   config.saveAllProcessingSteps = true;
%   config.enableStepSegmentation = true;
%   saveAllProcessingSteps(preprocessedSignals, intermediateResults, processedSignals,
%                         'data1.csv', './output', config);
%
%   注意事项:
%   - 各步骤数据格式必须一致（元胞数组）
%   - 分割参数在所有步骤中保持一致
%   - 文件夹按处理顺序编号（1-4）
%   - 支持选择性保存特定步骤
%   - 第一步保存的是预处理数据，不是原始CSV数据
%
%   参见: SAVEPREPROCESSEDDATA, SEGMENTANDSAVESTEPTIMETABLE, CREATEPROCESSINGCONFIG
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    %% 参数验证
    if nargin < 6
        error('saveAllProcessingSteps:NotEnoughInputs', '需要6个输入参数');
    end
    
    if ~config.saveAllProcessingSteps
        if config.enableVerboseOutput
            fprintf('分步结果保存已禁用，跳过处理\n');
        end
        return;
    end
    
    %% 步骤1：保存预处理数据
    if config.savePreprocessedData
        if config.enableVerboseOutput
            fprintf('\n=== 步骤1：保存预处理数据 ===\n');
        end
        savePreprocessedData(preprocessedSignals, originalFileName, outputFolder, config);
    end
    
    %% 步骤2：保存带通滤波结果
    if isfield(intermediateResults, 'bandpassFiltered') && ~isempty(intermediateResults.bandpassFiltered)
        if config.enableVerboseOutput
            fprintf('\n=== 步骤2：保存带通滤波结果 ===\n');
        end
        saveStepResult(intermediateResults.bandpassFiltered, originalFileName, outputFolder, config, 'bandpass');
    end
    
    %% 步骤3：保存谱减法结果
    if isfield(intermediateResults, 'spectralSubtracted') && ~isempty(intermediateResults.spectralSubtracted)
        if config.enableVerboseOutput
            fprintf('\n=== 步骤3：保存谱减法结果 ===\n');
        end
        saveStepResult(intermediateResults.spectralSubtracted, originalFileName, outputFolder, config, 'spectral');
    end
    
    %% 步骤4：保存最终结果
    if ~isempty(processedSignals)
        if config.enableVerboseOutput
            fprintf('\n=== 步骤4：保存最终结果 ===\n');
        end
        saveStepResult(processedSignals, originalFileName, outputFolder, config, 'final');
    end
    
    %% 输出总结信息
    if config.enableVerboseOutput
        fprintf('\n=== 所有处理步骤保存完成 ===\n');
        fprintf('输出文件夹结构:\n');
        if config.savePreprocessedData
            fprintf('  - %s/ (预处理数据：去直流、去趋势、归一化)\n', config.preprocessedDataFolder);
        end
        fprintf('  - %s/ (带通滤波结果)\n', config.bandpassResultsFolder);
        fprintf('  - %s/ (谱减法结果)\n', config.spectralResultsFolder);
        fprintf('  - %s/ (最终结果)\n', config.finalResultsFolder);
        fprintf('==============================\n\n');
    end
end

function saveStepResult(signalData, originalFileName, outputFolder, config, stepType)
%SAVESTEPRESULT 保存单个处理步骤的结果
%   对指定步骤的结果进行保存和分割处理
%
%   输入参数:
%   signalData      - 信号数据 (元胞数组)
%   originalFileName - 原始文件名
%   outputFolder    - 输出文件夹
%   config         - 配置参数
%   stepType       - 步骤类型 ('bandpass', 'spectral', 'final')

    try
        % 验证信号数据
        if isempty(signalData) || ~iscell(signalData)
            warning('saveAllProcessingSteps:InvalidSignalData', ...
                '%s结果数据无效，跳过处理', stepType);
            return;
        end
        
        % 获取信号数据
        column2 = signalData{1};
        column3 = signalData{2};
        
        if isempty(column2) || isempty(column3)
            warning('saveAllProcessingSteps:EmptySignalData', ...
                '%s结果数据为空，跳过处理', stepType);
            return;
        end
        
        % 确定输出文件夹
        switch stepType
            case 'bandpass'
                stepFolder = fullfile(outputFolder, config.bandpassResultsFolder);
            case 'spectral'
                stepFolder = fullfile(outputFolder, config.spectralResultsFolder);
            case 'final'
                stepFolder = fullfile(outputFolder, config.finalResultsFolder);
            otherwise
                error('saveAllProcessingSteps:UnknownStepType', '未知的步骤类型: %s', stepType);
        end
        
        % 创建步骤文件夹
        if ~exist(stepFolder, 'dir')
            mkdir(stepFolder);
            if config.enableVerboseOutput
                fprintf('创建%s结果文件夹: %s\n', stepType, stepFolder);
            end
        end
        
        % 创建时间表
        tt_ch2 = timetable(column2, 'SampleRate', config.samplingRate);
        tt_ch3 = timetable(column3, 'SampleRate', config.samplingRate);
        
        % 生成文件名
        [~, baseName, ~] = fileparts(originalFileName);
        cleanBaseName = regexprep(baseName, '[^a-zA-Z0-9_]', '_');
        if ~isempty(cleanBaseName) && ~isletter(cleanBaseName(1))
            cleanBaseName = ['file_', cleanBaseName];
        end
        
        % 保存完整的步骤结果文件
        if ~config.enableStepSegmentation
            % 只保存完整文件，不分割
            fullFileName = fullfile(stepFolder, [cleanBaseName, '_', stepType, '_tt.mat']);
            
            % 创建变量名
            var1Name = [cleanBaseName, '_', stepType, '_tt1'];
            var2Name = [cleanBaseName, '_', stepType, '_tt2'];
            
            % 保存文件
            eval([var1Name, ' = tt_ch2;']);
            eval([var2Name, ' = tt_ch3;']);
            save(fullFileName, var1Name, var2Name);
            
            if config.enableProgressDisplay
                fprintf('已保存%s完整结果: %s\n', stepType, [cleanBaseName, '_', stepType, '_tt.mat']);
            end
        else
            % 进行分割处理
            if config.enableVerboseOutput
                fprintf('对%s结果进行分割处理...\n', stepType);
            end
            
            % 调用分割函数，使用修改后的文件名前缀
            modifiedFileName = [cleanBaseName, '_', stepType];
            segmentAndSaveStepTimeTable(tt_ch2, tt_ch3, modifiedFileName, stepFolder, config, stepType);
        end
        
    catch ME
        if config.enableErrorHandling
            warning('saveAllProcessingSteps:StepProcessingFailed', ...
                '%s结果处理失败: %s', stepType, ME.message);
        else
            rethrow(ME);
        end
    end
end
