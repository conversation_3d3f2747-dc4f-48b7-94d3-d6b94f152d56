function plot_waveform_with_envelope(t, signal, envelope, peak_times, peaks, position, rows, cols, index, title_text, varargin)
%PLOT_WAVEFORM_WITH_ENVELOPE 肠鸣音信号包络和峰值绘制函数
%   绘制时域信号波形、包络线和检测到的峰值，专门用于肠鸣音音频信号的
%   峰值检测结果可视化分析。
%
%   语法:
%   plot_waveform_with_envelope(t, signal, envelope, peak_times, peaks, position, rows, cols, index, title_text)
%   plot_waveform_with_envelope(t, signal, envelope, peak_times, peaks, position, rows, cols, index, title_text, time_range)
%
%   输入参数:
%   t          - 时间向量 (数值数组，单位：秒)
%   signal     - 信号数据 (数值向量，归一化幅值)
%   envelope   - 信号包络 (数值向量)
%   peak_times - 峰值时间 (数值向量，单位：秒)
%   peaks      - 峰值数值 (数值向量)
%   position   - 图形窗口位置 ([left, bottom, width, height])
%   rows       - 子图行数 (正整数)
%   cols       - 子图列数 (正整数)
%   index      - 当前子图索引 (正整数)
%   title_text - 图形标题 (字符串)
%   time_range - 可选，时间轴范围 ([t_min, t_max]，单位：秒)

    % 解析可选的时间范围参数
    if nargin >= 11 && ~isempty(varargin{1})
        time_range = varargin{1};
    else
        % 默认使用数据的实际时间范围
        if isa(t, 'duration')
            time_range = [0, seconds(t(end))];
        else
            time_range = [min(t), max(t)];
        end
    end

    figure('Position', position); % 设置图框位置和大小，[left, bottom, width, height]
    subplot(rows, cols, index);
    
    % 处理时间格式 - 统一转换为数值（秒）
    if isa(t, 'duration')
        t_plot = seconds(t);
    else
        t_plot = t;
    end
    
    plot(t_plot, real(signal), 'k');
    hold on;
    plot(t_plot, envelope, 'g');
    plot(peak_times, peaks, 'o', 'MarkerEdgeColor', 'r', 'MarkerSize', 5);
    hold off;
    
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.XLim = time_range; % 使用动态时间范围
    ax.YLim = [-0.5 0.5]; % 调整为与plot_waveform一致的范围
    yticks([-0.5 -0.25 0 0.25 0.5]); % 自定义纵坐标刻度
    
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
    legend('Signal', 'Envelope', 'Peaks', 'FontSize', 14, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    grid on;
end