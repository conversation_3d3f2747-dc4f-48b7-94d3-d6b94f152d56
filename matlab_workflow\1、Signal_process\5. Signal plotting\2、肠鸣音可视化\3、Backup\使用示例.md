# photograph.m 使用示例

## 基本使用方法

### 1. 运行程序
```matlab
photograph
```

### 2. 选择数据文件
程序会弹出文件选择对话框，选择包含timetable数据的.mat文件。

### 3. 选择数据变量（如果有多个）
如果数据文件包含多个timetable变量，程序会显示选择界面：

```
发现多个timetable变量:
  [1] data4_5min_seg005_tt1 (数据点: 771000, 时长: 300.0秒)
  [2] data4_5min_seg005_tt2 (数据点: 771000, 时长: 300.0秒)

请选择要处理的变量 (1-2): 
```

输入数字选择要分析的数据：
- 输入 `1` 选择tt1数据
- 输入 `2` 选择tt2数据

### 4. 查看结果
程序会自动生成三个可视化图表：
- **波形图**：显示原始音频信号的时域特征
- **频谱图**：显示信号的时频域分布特征  
- **强度图**：显示肠鸣音在100-1000Hz频段的强度变化

## 功能特点

### 自动适应数据长度
- 程序会自动检测数据的实际时间长度
- X轴范围完全匹配数据长度，不再固定为0-300秒
- 支持任意长度的音频数据分析

### 智能时间刻度
根据数据长度自动生成合适的时间刻度：
- **小于1分钟**：每10秒一个刻度
- **1-5分钟**：每50秒一个刻度  
- **超过5分钟**：每60秒一个刻度

### 数据信息显示
程序会显示选择的数据信息：
```
您选择了: data4_5min_seg005_tt1
数据时间范围: 0.0 - 300.0 秒 (总时长: 300.0 秒)
```

## 支持的数据格式

### 文件要求
- **文件格式**：.mat文件
- **数据类型**：timetable格式
- **采样频率**：2570 Hz（程序内设定）
- **数据结构**：单列音频信号数据

### 变量命名
程序支持灵活的变量命名：
- 优先选择包含'tt1'的变量
- 支持任意命名的timetable变量
- 自动识别和列出所有可用变量

## 输出图表说明

### 1. 波形图 (Raw data of Mic)
- **Y轴**：归一化幅值 [-1, 1]
- **X轴**：时间（秒）
- **颜色**：深蓝色
- **用途**：观察信号的时域特征和质量

### 2. 频谱图 (Spectrogram of Mic)
- **Y轴**：频率（Hz）
- **X轴**：时间（秒）
- **颜色**：功率谱密度（dB），使用Inferno色彩映射
- **用途**：分析信号的时频域分布

### 3. 强度图 (Intensity of Mic)
- **Y轴**：强度值 [0, 0.025]
- **X轴**：时间（秒）
- **颜色**：深蓝色
- **频段**：100-1000Hz（肠鸣音主要频段）
- **用途**：量化肠鸣音活动强度

## 故障排除

### 常见问题

1. **"在数据文件中找不到任何timetable格式的变量"**
   - 检查数据文件是否包含timetable类型的变量
   - 确认数据文件格式正确

2. **"用户取消了文件选择"**
   - 重新运行程序并选择有效的数据文件

3. **"无效选择，请输入1到N之间的整数"**
   - 输入有效的数字选择变量
   - 确保输入的是整数且在有效范围内

### 技术要求
- MATLAB版本：建议R2018b或更高版本
- 工具箱：Signal Processing Toolbox
- 依赖函数：inferno.m（颜色映射函数）

## 示例数据
程序已在以下数据上测试：
- `data4_5min_seg005_tt_yes_15.mat`
- 包含变量：`data4_5min_seg005_tt1` 和 `data4_5min_seg005_tt2`
- 数据长度：300秒（5分钟）
- 采样点数：771,000点

## 更新历史
- **v1.0**：基础版本，固定300秒时间范围
- **v2.0**：增加交互式变量选择和动态时间范围功能
