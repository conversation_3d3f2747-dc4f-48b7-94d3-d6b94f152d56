function plot_pspectrum(signal, fs, position, rows, cols, index, title_text, varargin)
%PSPECTRUM 肠鸣音信号功率谱绘制函数
%   计算并绘制信号的功率谱密度，专门用于肠鸣音音频信号的频域分析。
%   该函数提供专业的科研级别功率谱可视化，适用于医学信号处理和
%   肠鸣音频域特征分析研究。
%
%   语法:
%   pspectrum(signal, fs, position, rows, cols, index, title_text)
%   pspectrum(signal, fs, position, rows, cols, index, title_text, time_range)
%   pspectrum(signal, fs, position, rows, cols, index, title_text, time_range, freq_range)
%
%   输入参数:
%   signal     - 输入信号数据 (数值向量)
%   fs         - 采样频率 (标量，单位：Hz，通常为2570Hz)
%   position   - 图形窗口位置 ([left, bottom, width, height])
%   rows       - 子图行数 (正整数)
%   cols       - 子图列数 (正整数)
%   index      - 当前子图索引 (正整数)
%   title_text - 图形标题 (字符串)
%   time_range - 可选，时间轴范围 ([t_min, t_max]，单位：秒)
%   freq_range - 可选，频率轴范围 ([f_min, f_max]，单位：Hz)
%
%   输出参数:
%   无 - 直接生成功率谱图显示
%
%   技术参数:
%   - 默认频率范围: [0, fs/2] Hz (奈奎斯特频率)
%   - 默认时间范围: 整个信号长度
%   - 功率谱算法: MATLAB内置pspectrum函数
%   - Y轴单位: dB (分贝)
%   - 颜色: 深蓝色 RGB[56,82,151]
%   - 字体设置: Times New Roman, 加粗, 16pt刻度, 18pt标签
%
%   图形特征:
%   - X轴: 频率 (Hz)
%   - Y轴: 功率谱密度 (dB)
%   - 网格: 开启，便于读数
%   - 线条宽度: 1.5pt，确保清晰度
%
%   应用场景:
%   - 肠鸣音信号频域特征分析
%   - 信号频率成分识别
%   - 噪声和信号分离评估
%   - 频域滤波效果验证
%
%   使用示例:
%   % 基本调用
%   pspectrum(signal, 2570, [100,100,800,600], 1, 1, 1, 'Power Spectrum');
%
%   % 指定时间范围
%   pspectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Power Spectrum', [0, 60]);
%
%   % 指定时间和频率范围
%   pspectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Power Spectrum', [0, 60], [0, 1000]);
%
%   注意事项:
%   - 输入信号应为一维数值向量
%   - 采样频率必须为正数
%   - 时间范围和频率范围应为递增的二元素向量
%   - 函数会自动处理信号长度和采样频率的匹配
%
%   错误处理:
%   - 输入参数类型和维度验证
%   - 采样频率有效性检查
%   - 时间和频率范围合理性验证
%   - 信号长度充足性检查
%
%   参见: PSPECTRUM, PLOT_WAVEFORM, PLOT_SPECTROGRAM, FFT
%
%   作者: [医学信号处理团队]
%   创建日期: 2025-08-26
%   最后修改: 2025-08-26
%   版本: 1.0

    % 输入参数验证
    if nargin < 7
        error('pspectrum:NotEnoughInputs', '至少需要7个输入参数');
    end

    % 验证信号数据
    if ~isnumeric(signal) || ~isvector(signal)
        error('pspectrum:InvalidSignal', '信号数据必须是数值向量');
    end

    % 验证采样频率
    if ~isnumeric(fs) || ~isscalar(fs) || fs <= 0
        error('pspectrum:InvalidSamplingRate', '采样频率必须是正数标量');
    end

    % 验证位置参数
    if ~isnumeric(position) || length(position) ~= 4
        error('pspectrum:InvalidPosition', '位置参数必须是4元素数值向量');
    end

    % 验证子图参数
    if ~isnumeric(rows) || ~isscalar(rows) || rows <= 0 || rows ~= round(rows)
        error('pspectrum:InvalidRows', '行数必须是正整数');
    end
    if ~isnumeric(cols) || ~isscalar(cols) || cols <= 0 || cols ~= round(cols)
        error('pspectrum:InvalidCols', '列数必须是正整数');
    end
    if ~isnumeric(index) || ~isscalar(index) || index <= 0 || index ~= round(index)
        error('pspectrum:InvalidIndex', '索引必须是正整数');
    end

    % 验证标题
    if ~ischar(title_text) && ~isstring(title_text)
        error('pspectrum:InvalidTitle', '标题必须是字符串');
    end
    
    % 处理可选参数
    time_range = [];
    freq_range = [];
    
    if nargin >= 8 && ~isempty(varargin{1})
        time_range = varargin{1};
        if ~isnumeric(time_range) || length(time_range) ~= 2 || time_range(1) >= time_range(2)
            error('pspectrum:InvalidTimeRange', '时间范围必须是递增的二元素向量');
        end
    end

    if nargin >= 9 && ~isempty(varargin{2})
        freq_range = varargin{2};
        if ~isnumeric(freq_range) || length(freq_range) ~= 2 || freq_range(1) >= freq_range(2)
            error('pspectrum:InvalidFreqRange', '频率范围必须是递增的二元素向量');
        end
    end
    
    % 确保信号为列向量
    signal = signal(:);
    
    % 计算信号时间向量
    N = length(signal);
    t = (0:N-1) / fs;
    
    % 处理时间范围
    if ~isempty(time_range)
        % 找到时间范围内的索引
        start_idx = max(1, round(time_range(1) * fs) + 1);
        end_idx = min(N, round(time_range(2) * fs) + 1);
        
        if start_idx >= end_idx
            error('pspectrum:InvalidTimeRange', '指定的时间范围超出信号长度');
        end
        
        % 截取信号
        signal = signal(start_idx:end_idx);
        t = t(start_idx:end_idx);
    end
    
    % 设置频率范围
    if isempty(freq_range)
        freq_range = [0, fs/2]; % 默认到奈奎斯特频率
    end
    
    % 计算功率谱
    try
        [P, F] = pspectrum(signal, fs, 'FrequencyLimits', freq_range);
    catch ME
        error('pspectrum:PspectrumError', '功率谱计算失败: %s', ME.message);
    end
    
    % 创建图形
    figure('Position', position);
    subplot(rows, cols, index);
    
    % 绘制功率谱
    color1 = [56/255, 82/255, 151/255]; % 深蓝色，与其他函数保持一致
    plot(F, 10*log10(P), 'Color', color1, 'LineWidth', 1.5);
    
    % 设置图形属性
    ax = gca;
    ax.FontSize = 16;
    ax.FontName = 'Times New Roman';
    ax.FontWeight = 'bold';
    
    % 设置坐标轴
    xlim(freq_range);
    xlabel('Frequency (Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Power Spectral Density (dB)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
    % 开启网格
    grid on;
    ax.GridAlpha = 0.3;
    
end
